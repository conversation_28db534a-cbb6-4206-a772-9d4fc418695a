from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
import numpy as np
import json

@dataclass
class FeatureData:
    """特征数据结构"""
    feature_id: str
    name: str  
    type: str   # 特征类型
    length: float = 0.0
    diameter: float = 0.0
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PartData:
    """零件数据结构"""
    part_id: str
    name: str
    length: float = 0.0
    width: float = 0.0
    height: float = 0.0
    area: float = 0.0
    volume: float = 0.0
    density: float = 0.0
    mass: float = 0.0
    material: Optional[str] = None
    hole_count: float = 0.0
    hole_diameter_mean: float = 0.0
    hole_diameter_std: float = 0.0
    hole_depth_mean: float = 0.0
    hole_depth_std: float = 0.0
    features: List[FeatureData] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    shape_embedding: Optional[np.ndarray] = None
    description: Optional[str] = None
    description_embedding: Optional[np.ndarray] = None

@dataclass
class SubAssemblyData:
    """子装配体数据结构"""
    subassembly_id: str
    name: str
    area: float = 0.0
    volume: float = 0.0
    density: float = 0.0
    mass: float = 0.0
    parts: List[PartData] = field(default_factory=list)
    subassemblies: List['SubAssemblyData'] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    shape_embedding: Optional[np.ndarray] = None
    description: Optional[str] = None
    description_embedding: Optional[np.ndarray] = None

@dataclass
class AssemblyData:
    """顶层装配体数据结构"""
    assembly_id: str
    name: str
    length: float = 0.0
    width: float = 0.0
    height: float = 0.0
    area: float = 0.0
    volume: float = 0.0
    density: float = 0.0
    mass: float = 0.0
    part_count: float = 0.0  # TODO: 应该是int
    industry: Optional[str] = None  # The project industry specified by the user (architecture, engineering & construction; civil infrastructure; media & entertainment; product design & manufacturing; other industries)
    category: Optional[str] = None  # The project category specified by the user (automotive, art, electronics, engineering, game, machine design, interior design, medical, product design, robotics, sport, tools, toys, etc.)
    parts: List[PartData] = field(default_factory=list)
    subassemblies: List[SubAssemblyData] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    shape_embedding: Optional[np.ndarray] = None
    description: Optional[str] = None
    description_embedding: Optional[np.ndarray] = None

@dataclass
class CADModelData:
    """CAD模型统一数据结构"""
    assembly: AssemblyData
    source_format: str  # 原始数据格式标识，例如 "fusion360", "onshape" 等
    metadata: Dict[str, Any] = field(default_factory=dict) 
    
    def print_assembly_tree(self):
        """
        打印CAD模型的装配层次结构树
        """
        print("\n=== CAD模型装配层次结构树 ===")
        self._print_assembly(self.assembly, 0)
    
    def _print_assembly(self, assembly, indent=0):
        """
        打印装配体信息
        
        Args:
            assembly: 装配体对象
            indent: 缩进级别
        """
        # 打印装配体信息
        print(f"{'  ' * indent}[装配体] {assembly.name} (ID: {assembly.assembly_id})")
        
        # 打印零件
        for part in assembly.parts:
            self._print_part(part, indent+1)
        
        # 递归打印子装配体
        for subassembly in assembly.subassemblies:
            self._print_subassembly(subassembly, indent+1)
    
    def _print_subassembly(self, subassembly, indent):
        """
        打印子装配体信息
        
        Args:
            subassembly: 子装配体对象
            indent: 缩进级别
        """
        # 检查是否为构造装配体
        is_construction = subassembly.properties.get('is_construction', False)
        prefix = "[虚拟装配体]" if is_construction else "[子装配体]"
        print(f"{'  ' * indent}{prefix} {subassembly.name} (ID: {subassembly.subassembly_id})")
        
        # 打印零件
        for part in subassembly.parts:
            self._print_part(part, indent+1)
        
        # 递归处理嵌套的子装配体
        for child in subassembly.subassemblies:
            self._print_subassembly(child, indent+1)
    
    def _print_part(self, part, indent):
        """
        打印零件信息
        
        Args:
            part: 零件对象
            indent: 缩进级别
        """
        print(f"{'  ' * indent}[零件] {part.name} (ID: {part.part_id})")
        
        # 打印特征
        for feature in part.features:
            print(f"{'  ' * (indent+1)}[特征] {feature.name} (ID: {feature.feature_id})")
    
    def to_json(self) -> str:
        """
        输出JSON格式的装配体信息
        
        Returns:
            str: JSON格式的装配体信息
        """
        json_data = {
            "industry": self.assembly.industry,
            "category": self.assembly.category,
            "hierarchy": self._assembly_to_dict(self.assembly)
        }
        return json.dumps(json_data, indent=2, ensure_ascii=False)
    
    def _assembly_to_dict(self, assembly: AssemblyData) -> Dict[str, Any]:
        """
        将装配体转换为字典格式
        
        Args:
            assembly: 装配体对象
            
        Returns:
            Dict[str, Any]: 装配体的字典表示
        """
        return {
            "parts": [self._part_to_dict(part) for part in assembly.parts],
            "subassemblies": [self._subassembly_to_dict(sub) for sub in assembly.subassemblies]
        }
    
    def _subassembly_to_dict(self, subassembly: SubAssemblyData) -> Dict[str, Any]:
        """
        将子装配体转换为字典格式
        
        Args:
            subassembly: 子装配体对象
            
        Returns:
            Dict[str, Any]: 子装配体的字典表示
        """
        return {
            "name": subassembly.name,
            "parts": [self._part_to_dict(part) for part in subassembly.parts],
            "subassemblies": [self._subassembly_to_dict(sub) for sub in subassembly.subassemblies]
        }
    
    def _part_to_dict(self, part: PartData) -> Dict[str, Any]:
        """
        将零件转换为字典格式
        
        Args:
            part: 零件对象
            
        Returns:
            Dict[str, Any]: 零件的字典表示
        """
        part_dict = {
            "name": part.name
        }
        
        # 只添加非空/非零的字段
        if part.material:
            part_dict["material"] = part.material
        if part.hole_count > 0:
            part_dict["hole_count"] = int(part.hole_count)
            
        return part_dict