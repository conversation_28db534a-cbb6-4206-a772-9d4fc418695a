"""
Text to Cypher Agent
将自然语言查询转换为Cypher查询的代理
"""

import os
from typing import Dict, Any, Optional
from src.models.LLM import MultiProviderLLM
from src.knowledge_graph import CADKnowledgeGraph


class Text2CypherAgent:
    """
    Text to Cypher Agent - 将自然语言查询转换为Cypher查询
    """
    
    def __init__(
        self,
        llm: Optional[MultiProviderLLM] = None,
        schema_file: str = "dataset/neo4j_schema_report1.md",
        kg: Optional[CADKnowledgeGraph] = None
    ):
        """
        初始化Text2Cypher Agent
        
        Args:
            llm: MultiProviderLLM实例，如果为None则使用默认配置
            schema_file: Neo4j schema文件路径
            kg: CADKnowledgeGraph实例，如果为None则创建新实例
        """
        self.llm = llm or MultiProviderLLM(
            provider="openai",
            api_url="http://**************:4000/",
            api_key="sk-YyiBg6DSn1Fc2KBNU6ZYtw",
            model="DeepSeek-V3-0324",
        )
        
        self.kg = kg or CADKnowledgeGraph.from_config()
        self.schema = self._load_schema(schema_file)
        self.system_prompt = self._build_system_prompt()
    
    def _load_schema(self, schema_file: str) -> str:
        """
        加载Neo4j schema信息
        
        Args:
            schema_file: schema文件路径
            
        Returns:
            schema内容字符串
        """
        try:
            # 处理相对路径
            if not os.path.isabs(schema_file):
                # 假设从项目根目录开始
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.join(current_dir, "..", "..")
                schema_file = os.path.join(project_root, schema_file)
            
            with open(schema_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"警告: 无法加载schema文件 {schema_file}: {e}")
            return self._get_default_schema()
    
    def _get_default_schema(self) -> str:
        """
        返回默认的schema信息
        """
        return """
        # Neo4j Schema
        
    ## 节点类型:
    - Assembly: 装配体 (属性: id, name, description, area, density, mass, volume, category, industry, shape_embedding, description_embedding)
    - SubAssembly: 子装配体 (属性: id, name, area, density, mass, volume)
    - Part: 零件 (属性: id, name, description, area, density, mass, volume, material, shape_embedding, description_embedding)
    - Feature: 制造特征，目前只有孔特征 (属性: id, name, type, diameter, length)
    
    ## 关系类型:
    - (Assembly)-[:hasSubAssembly]->(SubAssembly): 装配体包含子装配体
    - (SubAssembly)-[:hasSubAssembly]->(SubAssembly): 子装配体包含子装配体
    - (Assembly)-[:hasPart]->(Part): 装配体包含零件
    - (SubAssembly)-[:hasPart]->(Part): 子装配体包含零件
    - (Part)-[:hasFeature]->(Feature): 零件具有制造特征
        """
    
    def _build_system_prompt(self) -> str:
        """
        构建系统提示词
        """
        return f"""你是一个专业的Cypher查询生成器。你的任务是将用户的自然语言问题转换为准确的Cypher查询语句。

Neo4j数据库Schema信息:
{self.schema}

重要规则:
1. 只返回Cypher查询语句，不要包含任何解释或前言
2. 查询语句必须符合Neo4j Cypher语法
3. 使用正确的节点标签和关系类型
4. 对于模糊查询，尝试使用CONTAINS或正则表达式进行模糊匹配
5. 返回结果时要考虑用户的具体需求（计数、列表、详细信息等）, 节点的id和name是必不可少的
6. 如果需要聚合统计，使用适当的聚合函数

示例:
用户问题: "有多少个装配体?"
Cypher查询: MATCH (a:Assembly) RETURN count(a)

用户问题: "列出所有包含'bearing'的零件名称"
Cypher查询: MATCH (p:Part) WHERE p.name CONTAINS 'bearing' RETURN p.name, p.id

现在请根据用户的问题生成相应的Cypher查询。"""
    
    def generate_cypher(self, question: str) -> str:
        """
        根据自然语言问题生成Cypher查询
        
        Args:
            question: 用户的自然语言问题
            
        Returns:
            生成的Cypher查询字符串
        """
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"问题: {question}\nCypher查询:"}
            ]
            
            response = self.llm.chat(messages=messages)
            
            # 提取查询内容
            if hasattr(response, 'content'):
                cypher_query = response.content.strip()
            elif isinstance(response, dict) and 'content' in response:
                cypher_query = response['content'].strip()
            elif isinstance(response, str):
                cypher_query = response.strip()
            else:
                cypher_query = str(response).strip()
            
            # 清理可能的markdown代码块标记
            if cypher_query.startswith('```'):
                lines = cypher_query.split('\n')
                cypher_query = '\n'.join(lines[1:-1]) if len(lines) > 2 else cypher_query            
            return cypher_query
            
        except Exception as e:
            print(f"生成Cypher查询时出错: {e}")
            return f"// 错误: 无法生成查询 - {str(e)}"
    
    def chat(self, question: str) -> Dict[str, Any]:
        """
        处理用户问题并返回结构化响应
        
        Args:
            question: 用户问题
            
        Returns:
            包含原始问题、生成的Cypher查询、查询结果等信息的字典
        """
        cypher_query = self.generate_cypher(question)
        
        # 初始化返回结果
        result = {
            "question": question,
            "cypher_query": cypher_query,
            "status": "success" if not cypher_query.startswith("// 错误") else "error",
            "query_result": None,
            "error_message": None
        }
        
        # 如果Cypher查询生成成功，执行查询
        if result["status"] == "success":
            try:
                query_result = self.kg.run_cypher(cypher_query)
                result["query_result"] = query_result
                
                # 检查是否有错误返回
                if isinstance(query_result, dict) and "error" in query_result:
                    result["status"] = "error"
                    result["error_message"] = query_result.get("details", "查询执行失败")
                    
            except Exception as e:
                result["status"] = "error"
                result["error_message"] = f"执行Cypher查询时出错: {str(e)}"
        else:
            result["error_message"] = "Cypher查询生成失败"
        
        return result
    
    def batch_generate(self, questions: list) -> list:
        """
        批量生成Cypher查询
        
        Args:
            questions: 问题列表
            
        Returns:
            结果列表
        """
        results = []
        for question in questions:
            result = self.chat(question)
            results.append(result)
        
        return results


def create_text2cypher_agent(
    provider: str = "openai",
    api_url: str = "http://**************:4000/",
    api_key: str = "sk-YyiBg6DSn1Fc2KBNU6ZYtw",
    model: str = "DeepSeek-V3-0324"
) -> Text2CypherAgent:
    """
    创建Text2Cypher Agent的便捷函数
    
    Args:
        provider: LLM提供商
        api_url: API URL
        api_key: API密钥
        model: 模型名称
        
    Returns:
        Text2CypherAgent实例
    """
    llm = MultiProviderLLM(
        provider=provider,
        api_url=api_url,
        api_key=api_key,
        model=model
    )
    
    return Text2CypherAgent(llm=llm)


if __name__ == "__main__":
    # 测试代码
    agent = create_text2cypher_agent()
    
    # 测试问题
    test_questions = [
        "包含Burner_Pipe和Inside的模型",
    ]
    
    print("Text2Cypher Agent 测试:")
    print("=" * 50)
    
    for question in test_questions:
        result = agent.chat(question)
        print(f"问题: {result['question']}")
        print(f"Cypher: {result['cypher_query']}")
        print(f"状态: {result['status']}")
        
        if result['status'] == 'success' and result['query_result']:
            print(f"查询结果: {result['query_result']}")
        elif result['error_message']:
            print(f"错误信息: {result['error_message']}")
            
        print("-" * 30)
