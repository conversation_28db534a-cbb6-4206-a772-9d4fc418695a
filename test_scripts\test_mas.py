import asyncio
import os
import sys
# 添加项目根目录到系统路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(root_path)
sys.path.append(root_path)

from src.agent.mas_system_simple import create_mas_system

async def main():
    mas = await create_mas_system()
    
    try:
        # 文字查询
        result1 = await mas.query(text="assemblies with a mass greater than 10")
        
        # # 图片查询  
        # result2 = await mas.query(image=shape_embedding_vector)
        
        # # 混合查询
        # result3 = await mas.query(text="相似零件", image=shape_embedding_vector)
        
    finally:
        await mas.stop()

asyncio.run(main())