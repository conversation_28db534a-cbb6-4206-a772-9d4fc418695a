"""
几何和语义查询智能体 (Geometric and Semantic Query Agent)
与Milvus向量数据库交互，实现相似度搜索功能
"""

import logging
from typing import List, Dict, Any, Optional
import numpy as np
from src.agent.base_agent import BaseAgent
from src.agent.data_models import VectorSearchTask, QueryResult
from src.utils.database.milvus_utils import MilvusManager
from src.config import Config

logger = logging.getLogger(__name__)


class GeometrySemanticAgent(BaseAgent):
    """
    几何和语义查询智能体
    
    该智能体专门处理向量相似性搜索，支持以下三种搜索模式：
    1. 几何相似性检索：基于3D形状的视觉特征向量进行相似性搜索
    2. 深层语义检索：基于文本描述的语义理解进行相似性搜索
    3. 混合检索：结合文本语义和几何形状的综合搜索
    """
    
    def __init__(self, agent_name: str = "GeometrySemanticAgent"):
        super().__init__(agent_name)
        self.milvus_manager = None
        self.collection_name = "assembly_collection"
        self._connected = False
        
    async def connect(self):
        """连接到Milvus向量数据库"""
        try:
            # 初始化Milvus管理器
            self.milvus_manager = MilvusManager(use_reranker=True, device="cpu")
            await self._connect_milvus()
            logger.info(f"{self.agent_name} 成功连接到Milvus数据库")
        except Exception as e:
            logger.error(f"{self.agent_name} 连接Milvus失败: {e}")
            raise
    
    async def _connect_milvus(self):
        """异步连接Milvus"""
        self.milvus_manager.connect()
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.milvus_manager:
            # Milvus连接会自动管理，这里可以释放资源
            logger.info(f"{self.agent_name} 已断开Milvus连接")
    
    async def _ensure_connected(self):
        """确保已连接到数据库"""
        if not self._connected:
            await self.connect()
            self._connected = True
    
    async def execute_task(self, task: VectorSearchTask) -> QueryResult:
        """
        执行向量搜索任务
        
        Args:
            task: VectorSearchTask实例，包含查询向量和搜索参数
            
        Returns:
            QueryResult: 包含搜索结果的查询结果对象
        """
        try:
            await self._ensure_connected()
            
            results = []
            
            if task.search_partition == "geometric":
                # 几何相似性搜索
                results = await self._search_by_geometry(task.query_vector, task.top_k)
            elif task.search_partition == "semantic":
                # 语义相似性搜索
                results = await self._search_by_semantics(task.query_vector, task.top_k)
            else:
                # 默认搜索（可能是混合搜索）
                results = await self._search_hybrid(task.query_vector, task.top_k)
            
            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=results
            )
            
        except Exception as e:
            logger.error(f"{self.agent_name} 执行任务失败: {e}")
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[]
            )
    
    async def _search_by_geometry(self, query_vector: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        基于几何形状向量进行相似性搜索
        
        Args:
            query_vector: 形状特征向量（768维）
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            # 验证向量维度
            if len(query_vector) != 768:
                raise ValueError(f"几何向量维度应为768，实际为{len(query_vector)}")
            
            results = self.milvus_manager.search_assemblies_by_shape(
                collection_name=self.collection_name,
                query_vector=query_vector,
                top_k=top_k
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "geometry"
                })
            
            logger.info(f"几何搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"几何相似性搜索失败: {e}")
            raise
    
    async def _search_by_semantics(self, query_vector: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        基于语义向量进行相似性搜索
        
        注意：这里的query_vector应该是文本嵌入向量，或者可以接受文本查询
        
        Args:
            query_vector: 语义特征向量或文本查询
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            # 如果传入的是文本查询而不是向量，需要先转换
            # 这里假设query_vector是已经编码好的语义向量
            # 实际使用时可能需要根据具体情况调整
            
            # 使用Milvus管理器的文本搜索功能
            # 注意：这里需要传入原始文本查询，不是向量
            # 如果只有向量，可能需要额外的处理逻辑
            
            # 为了演示，这里假设我们有一个通过向量进行语义搜索的方法
            # 实际实现时可能需要调整
            raise NotImplementedError("语义向量搜索需要根据具体向量格式实现")
            
        except Exception as e:
            logger.error(f"语义相似性搜索失败: {e}")
            raise
    
    async def _search_hybrid(self, query_vector: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        混合搜索（几何+语义）
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            # 混合搜索需要同时有文本查询和形状向量
            # 这里作为示例，假设使用几何搜索
            return await self._search_by_geometry(query_vector, top_k)
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise
    
    async def search_by_text(self, query_text: str, top_k: int = 5, use_reranker: bool = True) -> List[Dict[str, Any]]:
        """
        基于文本查询进行语义搜索
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            use_reranker: 是否使用重排序器
            
        Returns:
            搜索结果列表
        """
        try:
            if self.milvus_manager is None:
                await self.connect()
            
            # 设置重排序器使用状态
            self.milvus_manager.use_reranker = use_reranker
            
            results = self.milvus_manager.search_assemblies_by_text(
                collection_name=self.collection_name,
                query_text=query_text,
                top_k=top_k
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "semantic"
                })
            
            logger.info(f"语义搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"文本语义搜索失败: {e}")
            raise
    
    async def search_by_shape(self, shape_vector: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        基于形状向量进行几何搜索
        
        Args:
            shape_vector: 形状特征向量（768维）
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        return await self._search_by_geometry(shape_vector, top_k)
    
    async def search_hybrid_with_shape(
        self, 
        query_text: str, 
        shape_vector: Optional[List[float]] = None, 
        top_k: int = 5, 
        shape_weight: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        混合搜索（文本语义 + 形状几何）
        
        Args:
            query_text: 查询文本
            shape_vector: 形状特征向量（可选）
            top_k: 返回结果数量
            shape_weight: 形状搜索权重（0-1之间）
            
        Returns:
            搜索结果列表
        """
        try:
            if self.milvus_manager is None:
                await self.connect()
            
            results = self.milvus_manager.search_assemblies_hybrid_with_shape(
                collection_name=self.collection_name,
                query_text=query_text,
                query_shape_vector=shape_vector,
                top_k=top_k,
                shape_weight=shape_weight
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "hybrid",
                    "has_shape_vector": shape_vector is not None,
                    "shape_weight": shape_weight
                })
            
            logger.info(f"混合搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise
