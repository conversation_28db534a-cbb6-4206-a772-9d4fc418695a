#!/usr/bin/env python3
"""
智能体测试启动器
Agent Test Launcher
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_test(test_file):
    """运行指定的测试文件"""
    test_path = project_root / "test_scripts" / test_file
    
    if not test_path.exists():
        print(f"测试文件不存在: {test_path}")
        return False
    
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        # 使用当前Python解释器运行测试
        result = subprocess.run([
            sys.executable, str(test_path)
        ], cwd=str(project_root), capture_output=True, text=True)
        
        print("标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {test_file} 测试成功完成")
            return True
        else:
            print(f"✗ {test_file} 测试失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"运行测试时发生异常: {e}")
        return False

def main():
    """主函数"""
    print("智能体测试启动器")
    print("可用的测试:")
    print("1. test_geometric_semantic_agent.py - 几何语义智能体测试")
    print("2. test_structured_data_agent.py - 结构化数据智能体测试") 
    print("3. test_structural_relationship_agent.py - 结构关系智能体测试")
    print("4. test_all_agents.py - 综合测试")
    print("5. 运行所有测试")
    
    # 测试文件列表
    test_files = [
        "test_geometric_semantic_agent.py",
        "test_structured_data_agent.py", 
        "test_structural_relationship_agent.py",
        "test_all_agents.py"
    ]
    
    while True:
        try:
            choice = input("\n请选择要运行的测试 (1-5, q退出): ").strip().lower()
            
            if choice == 'q':
                print("退出测试")
                break
            elif choice in ['1', '2', '3', '4']:
                test_index = int(choice) - 1
                run_test(test_files[test_index])
            elif choice == '5':
                print("运行所有测试...")
                success_count = 0
                for test_file in test_files:
                    if run_test(test_file):
                        success_count += 1
                
                print(f"\n{'='*60}")
                print(f"测试总结: {success_count}/{len(test_files)} 个测试成功")
                print(f"{'='*60}")
            else:
                print("无效选择，请输入 1-5 或 q")
                
        except KeyboardInterrupt:
            print("\n\n用户中断测试")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    # 检查是否在正确的目录
    if not (project_root / "src" / "agent").exists():
        print(f"错误: 未找到智能体模块目录")
        print(f"当前项目根目录: {project_root}")
        print("请确保在正确的项目目录中运行此脚本")
        sys.exit(1)
    
    main()
