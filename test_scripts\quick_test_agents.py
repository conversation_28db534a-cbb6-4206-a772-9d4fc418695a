"""
快速测试多智能体系统
简化版本，用于快速验证功能
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.agent_manager import AgentManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def quick_test():
    """快速测试三个智能体的基本功能"""
    print("开始快速测试多智能体系统...")
    
    try:
        async with AgentManager() as manager:
            print("✓ 所有智能体连接成功")
            
            # 1. 测试文本语义搜索
            print("\n1. 测试文本语义搜索...")
            try:
                result = await manager.execute_text_search(
                    query_text="圆形零件",
                    top_k=3
                )
                if result.status == 'success':
                    print(f"✓ 文本搜索成功，返回 {result.total_results} 个结果")
                    for item in result.results[:2]:
                        print(f"  - {item.id}: {item.description[:50]}...")
                else:
                    print(f"✗ 文本搜索失败: {result.error_message}")
            except Exception as e:
                print(f"✗ 文本搜索测试失败: {e}")
            
            # 2. 测试结构化数据查询
            print("\n2. 测试结构化数据查询...")
            try:
                result = await manager.execute_structured_query(
                    query_text="查找前5个零件的名称和质量"
                )
                if result.status == 'success':
                    print(f"✓ 结构化查询成功，返回 {result.total_results} 个结果")
                    for item in result.results[:2]:
                        print(f"  - {item.name}: {item.metadata.get('mass', 'N/A')}g")
                else:
                    print(f"✗ 结构化查询失败: {result.error_message}")
            except Exception as e:
                print(f"✗ 结构化查询测试失败: {e}")
            
            # 3. 测试结构关系查询
            print("\n3. 测试结构关系查询...")
            try:
                result = await manager.execute_structural_query(
                    query_text="MATCH (n:Assembly) RETURN n.name as name, n.id as id LIMIT 3"
                )
                if result.status == 'success':
                    print(f"✓ 结构关系查询成功，返回 {result.total_results} 个结果")
                    for item in result.results[:2]:
                        print(f"  - {item.name or item.id}")
                else:
                    print(f"✗ 结构关系查询失败: {result.error_message}")
            except Exception as e:
                print(f"✗ 结构关系查询测试失败: {e}")
            
            print("\n快速测试完成！")
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(quick_test())
