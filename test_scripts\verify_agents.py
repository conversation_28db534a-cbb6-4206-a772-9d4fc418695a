#!/usr/bin/env python3
"""
快速验证智能体基本功能
Quick Agent Functionality Verification
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.agent.data_models import (
            GeometricQuery, SemanticQuery, 
            StructuredQuery, StructuralQuery
        )
        print("✓ 数据模型导入成功")
    except Exception as e:
        print(f"✗ 数据模型导入失败: {e}")
        return False
    
    try:
        from src.agent.geometric_semantic_agent import GeometricSemanticAgent
        print("✓ 几何语义智能体导入成功")
    except Exception as e:
        print(f"✗ 几何语义智能体导入失败: {e}")
    
    try:
        from src.agent.structured_data_agent import StructuredDataAgent
        print("✓ 结构化数据智能体导入成功")
    except Exception as e:
        print(f"✗ 结构化数据智能体导入失败: {e}")
    
    try:
        from src.agent.structural_relationship_agent import StructuralRelationshipAgent
        print("✓ 结构关系智能体导入成功")
    except Exception as e:
        print(f"✗ 结构关系智能体导入失败: {e}")
    
    return True

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from src.config import Config
        config = Config()
        print("✓ 配置类加载成功")
        
        # 检查数据库配置
        if hasattr(config, 'database'):
            print("✓ 数据库配置存在")
        else:
            print("⚠ 数据库配置不存在")
        
        if hasattr(config, 'neo4j'):
            print("✓ Neo4j配置存在")
        else:
            print("⚠ Neo4j配置不存在")
        
        if hasattr(config, 'milvus'):
            print("✓ Milvus配置存在")
        else:
            print("⚠ Milvus配置不存在")
            
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_data_models():
    """测试数据模型"""
    print("\n测试数据模型...")
    
    try:
        from src.agent.data_models import (
            GeometricQuery, SemanticQuery, 
            StructuredQuery, StructuralQuery
        )
        
        # 测试语义查询
        semantic_query = SemanticQuery(
            query_text="测试查询",
            limit=5,
            similarity_threshold=0.7
        )
        print("✓ 语义查询模型创建成功")
        
        # 测试几何查询
        geometric_query = GeometricQuery(
            feature_vector=[0.1] * 512,
            limit=5,
            similarity_threshold=0.8
        )
        print("✓ 几何查询模型创建成功")
        
        # 测试结构化查询
        structured_query = StructuredQuery(
            query_text="测试SQL查询",
            table_name="parts",
            limit=10
        )
        print("✓ 结构化查询模型创建成功")
        
        # 测试结构关系查询
        structural_query = StructuralQuery(
            query_text="测试Cypher查询",
            node_type="Part",
            limit=10
        )
        print("✓ 结构关系查询模型创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        return False

def test_agent_initialization():
    """测试智能体初始化"""
    print("\n测试智能体初始化...")
    
    # 测试几何语义智能体
    try:
        from src.agent.geometric_semantic_agent import GeometricSemanticAgent
        agent = GeometricSemanticAgent()
        print("✓ 几何语义智能体初始化成功")
    except Exception as e:
        print(f"✗ 几何语义智能体初始化失败: {e}")
    
    # 测试结构化数据智能体
    try:
        from src.agent.structured_data_agent import StructuredDataAgent
        agent = StructuredDataAgent()
        print("✓ 结构化数据智能体初始化成功")
    except Exception as e:
        print(f"✗ 结构化数据智能体初始化失败: {e}")
    
    # 测试结构关系智能体
    try:
        from src.agent.structural_relationship_agent import StructuralRelationshipAgent
        agent = StructuralRelationshipAgent()
        print("✓ 结构关系智能体初始化成功")
    except Exception as e:
        print(f"✗ 结构关系智能体初始化失败: {e}")

def main():
    """主函数"""
    print("智能体快速验证")
    print("=" * 50)
    
    # 检查项目结构
    agent_dir = project_root / "src" / "agent"
    if not agent_dir.exists():
        print(f"✗ 智能体目录不存在: {agent_dir}")
        return
    
    print(f"✓ 项目根目录: {project_root}")
    print(f"✓ 智能体目录: {agent_dir}")
    
    # 执行测试
    success = True
    success &= test_imports()
    success &= test_config()
    success &= test_data_models()
    test_agent_initialization()  # 不影响总体成功状态
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 基本验证通过，智能体模块准备就绪")
    else:
        print("✗ 验证发现问题，请检查配置和依赖")
    print("=" * 50)

if __name__ == "__main__":
    main()
