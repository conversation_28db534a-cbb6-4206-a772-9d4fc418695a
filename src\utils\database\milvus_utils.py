#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Milvus数据库管理工具

提供Milvus的连接、集合创建和检索功能
"""

import os
import logging
from typing import List, Dict, Any, Optional

from pymilvus import (
    connections, utility, FieldSchema, CollectionSchema, DataType,
    Collection, AnnSearchRequest, RRFRanker
)
from pymilvus.model.hybrid import BGEM3EmbeddingFunction
from pymilvus.model.reranker import BGERerankFunction
import torch
import numpy as np

from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置环境变量，使用国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'


class MilvusManager:
    """
    Milvus数据库管理类
    
    提供Milvus的连接、集合创建和检索功能
    """
    
    def __init__(self, use_reranker=True, device="cpu"):
        """
        初始化Milvus管理器
        
        参数:
            use_reranker: 是否使用重排序器
            device: 使用的设备，'cpu'或'cuda'
        """
        self.use_reranker = use_reranker
        self.device = device
        self.connected = False
        
        # 检查是否使用GPU
        if self.device == "cuda" and not torch.cuda.is_available():
            logger.warning("未检测到CUDA设备，将使用CPU代替")
            self.device = "cpu"
        
        self.model_path = os.path.expanduser("~/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181")
        if not os.path.exists(self.model_path):
            # 如果本地缓存不存在，使用模型名称让库自动下载
            self.model_path = "BAAI/bge-m3"
            logger.info(f"本地模型缓存不存在，将从Hugging Face下载模型: {self.model_path}")
        
        logger.info(f"使用设备: {self.device}")
        
        # 使用指定设备创建嵌入函数
        try:
            # 根据设备类型确定是否使用半精度
            use_fp16 = self.device == "cuda"
            self.embedding_function = BGEM3EmbeddingFunction(
                model_name=self.model_path, 
                use_fp16=use_fp16, 
                device=self.device
            )
            
            # 如果启用重排序，初始化BGE重排序函数
            if self.use_reranker:
                self.rerank_function = BGERerankFunction(device=self.device)
                
            logger.info("嵌入和重排序模型初始化成功")
        except Exception as e:
            logger.error(f"初始化模型时出错: {e}")
            raise
    
    def connect(self):
        """连接到Milvus服务器"""
        if self.connected:
            return
            
        # 从配置获取Milvus连接信息
        milvus_config = Config.get_milvus_config()
        host = milvus_config["host"]
        port = milvus_config["port"]
        
        try:
            connections.connect("default", host=host, port=port)
            logger.info(f"成功连接到Milvus服务器 {host}:{port}")
            self.connected = True
        except Exception as e:
            logger.error(f"连接Milvus服务器失败: {e}")
            raise
    
    def create_assembly_collection(self, collection_name="assembly_collection", recreate=False):
        """
        创建用于存储装配体信息的Milvus集合
        
        参数:
            collection_name: 集合名称
            recreate: 如果集合存在，是否重新创建
        
        返回:
            Collection对象
        """
        self.connect()
        
        # 检查集合是否已存在
        if utility.has_collection(collection_name):
            if recreate:
                logger.info(f"集合 '{collection_name}' 已存在，将被删除并重新创建")
                utility.drop_collection(collection_name)
            else:
                logger.info(f"集合 '{collection_name}' 已存在，将直接使用")
                return Collection(name=collection_name)
        
        # 创建集合字段
        fields = [
            # 主键字段，使用装配体ID作为主键
            FieldSchema(name="pk", dtype=DataType.VARCHAR, is_primary=True, max_length=100),
            # 装配体描述文本
            FieldSchema(name="description", dtype=DataType.VARCHAR, max_length=4096),
            # 密集向量字段
            FieldSchema(name="dense_vector", dtype=DataType.FLOAT_VECTOR, dim=1024),
            # 稀疏向量字段
            FieldSchema(name="sparse_vector", dtype=DataType.SPARSE_FLOAT_VECTOR),
            # 形状向量字段 (基于CLIP模型的视觉特征，维度为768)
            FieldSchema(name="shape_vector", dtype=DataType.FLOAT_VECTOR, dim=768)
        ]
        
        # 创建集合模式
        schema = CollectionSchema(fields=fields, description="装配体描述和形状向量集合")
        
        # 创建集合
        collection = Collection(name=collection_name, schema=schema)
        
        # 为密集向量创建索引
        dense_index = {"index_type": "FLAT", "metric_type": "IP"}
        collection.create_index("dense_vector", dense_index)
        
        # 为稀疏向量创建索引
        sparse_index = {"index_type": "SPARSE_INVERTED_INDEX", "metric_type": "IP"}
        collection.create_index("sparse_vector", sparse_index)
        
        # 为形状向量创建索引
        shape_index = {"index_type": "FLAT", "metric_type": "IP"}
        collection.create_index("shape_vector", shape_index)
        
        logger.info(f"已成功创建集合 '{collection_name}' 并添加索引")
        
        return collection
    
    def embed_descriptions(self, descriptions: List[str], batch_size=8) -> Dict[str, Any]:
        """
        使用BGE-M3模型将描述转换为密集和稀疏向量，支持批处理
        
        参数:
            descriptions: 描述文本列表
            batch_size: 批处理大小，用于控制内存占用
        
        返回:
            包含密集和稀疏向量的字典
        """
        try:
            if len(descriptions) <= batch_size:
                # 如果数据量小于批处理大小，直接处理
                return self.embedding_function(descriptions)
            else:
                # 批处理嵌入
                logger.info(f"描述数量较多 ({len(descriptions)}个)，使用批处理嵌入 (批大小={batch_size})")
                
                # 初始化结果容器
                all_dense = []
                all_sparse = []
                
                # 分批处理
                for i in range(0, len(descriptions), batch_size):
                    batch = descriptions[i:i+batch_size]
                    logger.info(f"处理批次 {i//batch_size + 1}/{(len(descriptions)-1)//batch_size + 1} ({len(batch)}个描述)")
                    
                    # 获取当前批次的嵌入
                    batch_embeddings = self.embedding_function(batch)
                    
                    # 收集结果
                    all_dense.extend(batch_embeddings["dense"])
                    all_sparse.extend(batch_embeddings["sparse"])
                
                return {
                    "dense": all_dense,
                    "sparse": all_sparse
                }
        except Exception as e:
            logger.error(f"生成嵌入向量时出错: {e}")
            raise
    
    def insert_assemblies(self, collection_name, assembly_ids, descriptions, shape_embeddings=None, batch_size=8):
        """
        将装配体信息插入到Milvus集合中
        
        参数:
            collection_name: 集合名称
            assembly_ids: 装配体ID列表
            descriptions: 描述文本列表
            shape_embeddings: 形状向量列表，如果为None则使用默认全零向量
            batch_size: 嵌入批处理大小，用于控制内存占用
            
        返回:
            插入的实体数量
        """
        self.connect()
        collection = Collection(name=collection_name)
        
        # 生成嵌入向量
        embeddings = self.embed_descriptions(descriptions, batch_size=batch_size)
        
        # 处理形状向量，如果未提供则使用默认全零向量
        if shape_embeddings is None:
            # 形状向量维度为768
            shape_embeddings = [np.zeros(768, dtype=np.float32) for _ in range(len(assembly_ids))]
            logger.warning(f"未提供形状向量，将使用全零向量替代 (共 {len(assembly_ids)} 个装配体)")
        elif len(shape_embeddings) != len(assembly_ids):
            raise ValueError(f"形状向量数量 ({len(shape_embeddings)}) 与装配体ID数量 ({len(assembly_ids)}) 不匹配")
        
        # 准备插入数据
        entities = [
            assembly_ids,                 # pk
            descriptions,                 # description
            embeddings["dense"],          # dense_vector
            embeddings["sparse"],         # sparse_vector
            shape_embeddings              # shape_vector
        ]
        
        # 插入数据到Milvus
        insert_result = collection.insert(entities)
        collection.flush()
        
        logger.info(f"成功将 {len(assembly_ids)} 个装配体描述和形状向量插入Milvus")
        
        return len(assembly_ids)
    
    def update_shape_embeddings(self, collection_name, assembly_ids, shape_embeddings):
        """
        更新已有装配体的形状向量
        
        参数:
            collection_name: 集合名称
            assembly_ids: 装配体ID列表
            shape_embeddings: 形状向量列表
        
        返回:
            更新的实体数量
        """
        self.connect()
        collection = Collection(name=collection_name)
        
        # 确保集合已加载到内存
        logger.info(f"正在加载集合 '{collection_name}' 到内存")
        collection.load()
        
        if len(assembly_ids) != len(shape_embeddings):
            raise ValueError(f"装配体ID数量 ({len(assembly_ids)}) 与形状向量数量 ({len(shape_embeddings)}) 不匹配")
        
        # 记录成功更新的数量
        success_count = 0
        
        for i, (assembly_id, shape_vector) in enumerate(zip(assembly_ids, shape_embeddings)):
            try:
                # 查询现有记录
                query_result = collection.query(expr=f'pk == "{assembly_id}"', output_fields=["description", "dense_vector", "sparse_vector"])
                
                if not query_result:
                    logger.warning(f"装配体 {assembly_id} 不存在，无法更新形状向量")
                    continue
                
                # 从查询结果中获取现有字段
                existing_record = query_result[0]
                description = existing_record.get("description", "")
                dense_vector = existing_record.get("dense_vector", [])
                sparse_vector = existing_record.get("sparse_vector", {})
                
                # 使用主键条件更新所有字段
                collection.upsert([
                    [assembly_id],      # pk
                    [description],      # description
                    [dense_vector],     # dense_vector
                    [sparse_vector],    # sparse_vector
                    [shape_vector]      # shape_vector
                ], 
                field_names=["pk", "description", "dense_vector", "sparse_vector", "shape_vector"])
                
                success_count += 1
                
                # 每处理100个打印一次日志
                if (i + 1) % 100 == 0 or i == len(assembly_ids) - 1:
                    logger.info(f"已更新 {i+1}/{len(assembly_ids)} 个装配体的形状向量")
                    
            except Exception as e:
                logger.error(f"更新装配体 {assembly_id} 的形状向量时出错: {e}")
          # 确保数据持久化
        collection.flush()
        logger.info(f"成功更新了 {success_count}/{len(assembly_ids)} 个装配体的形状向量")
        
        return success_count
    
    def search_assemblies_by_text(self, collection_name, query_text, top_k=3):
        """
        搜索装配体
        
        参数:
            collection_name: 集合名称
            query_text: 查询文本
            top_k: 返回结果数量
            
        返回:
            包含装配体ID、描述和相似度分数的搜索结果列表
        """
        self.connect()
        collection = Collection(name=collection_name)
        
        # 加载集合到内存以便搜索
        collection.load()
        
        # 生成查询向量
        query_embeddings = self.embed_descriptions([query_text])
        
        # 准备搜索请求
        sparse_search_params = {"metric_type": "IP"}
        sparse_req = AnnSearchRequest(
            query_embeddings["sparse"],
            "sparse_vector", 
            sparse_search_params, 
            limit=top_k
        )
        
        dense_search_params = {"metric_type": "IP"}
        dense_req = AnnSearchRequest(
            query_embeddings["dense"],
            "dense_vector", 
            dense_search_params, 
            limit=top_k
        )
        
        # 执行混合搜索
        results = collection.hybrid_search(
            [sparse_req, dense_req], 
            rerank=RRFRanker(),
            limit=top_k, 
            output_fields=["pk", "description"]
        )
        
        # 如果启用了重排序，使用BGE重排序器进一步优化结果
        if self.use_reranker:
            # 提取原始结果信息
            original_results = []
            for hit in results[0]:
                original_results.append({
                    'id': hit.fields["pk"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            
            result_texts = [hit.fields["description"] for hit in results[0]]
            
            # 对结果重新排序
            reranked_results = self.rerank_function(query_text, result_texts, top_k=top_k)
            
            logger.info("使用BGE CrossEncoder模型重新排序结果")
            
            # 构建重排序后的结果格式
            formatted_results = []
            for rerank_result in reranked_results:
                # 找到对应的原始结果
                original_result = next(
                    (r for r in original_results if r['description'] == rerank_result.text),
                    None
                )
                
                formatted_results.append({
                    'id': original_result['id'] if original_result else 'unknown',
                    'description': rerank_result.text,
                    'score': rerank_result.score
                })
            
            return formatted_results
        else:
            # 返回原始搜索结果，格式化为统一格式
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    'id': hit.fields["pk"],
                    'description': hit.fields["description"],
                    'score': hit.score                })
            
            return formatted_results
        
    def search_assemblies_by_shape(self, collection_name, query_vector, top_k=3):
        """
        通过形状向量搜索相似装配体
        
        参数:
            collection_name: 集合名称
            query_vector: 查询形状向量
            top_k: 返回结果数量
            
        返回:
            包含装配体ID、描述和相似度分数的搜索结果列表
        """
        self.connect()
        collection = Collection(name=collection_name)
        
        # 加载集合到内存以便搜索
        collection.load()
        
        # 确保查询向量格式正确
        if hasattr(query_vector, 'tolist'):
            query_vector = query_vector.tolist()
        
        # 准备搜索参数
        search_params = {"metric_type": "IP"}
        
        # 执行搜索
        results = collection.search(
            data=[query_vector],           # 查询向量
            anns_field="shape_vector",     # 搜索字段
            param=search_params,           # 搜索参数
            limit=top_k,                   # 返回结果数量
            output_fields=["pk", "description"]  # 返回的字段
        )
        
        logger.info(f"基于形状向量搜索到 {len(results[0])} 个相似装配体")
        
        # 格式化返回结果
        formatted_results = []
        for hit in results[0]:
            formatted_results.append({
                'id': hit.fields["pk"],
                'description': hit.fields["description"],
                'score': hit.score
            })        
        return formatted_results

    def search_assemblies_hybrid_with_shape(self, collection_name, query_text, query_shape_vector=None, top_k=3, shape_weight=0.3):
        """
        TODO: 未验证
        混合搜索装配体，结合文本描述和形状向量
        
        参数:
            collection_name: 集合名称
            query_text: 查询文本
            query_shape_vector: 查询形状向量，如果为None则仅使用文本搜索
            top_k: 返回结果数量
            shape_weight: 形状搜索权重 (0-1之间)，文本搜索权重为 1-shape_weight
            
        返回:
            包含装配体ID、描述和相似度分数的搜索结果列表
        """
        self.connect()
        collection = Collection(name=collection_name)
        
        # 加载集合到内存以便搜索
        collection.load()
        
        # 生成文本查询向量
        query_embeddings = self.embed_descriptions([query_text])
        
        # 准备文本搜索请求
        sparse_search_params = {"metric_type": "IP"}
        sparse_req = AnnSearchRequest(
            query_embeddings["sparse"],
            "sparse_vector", 
            sparse_search_params, 
            limit=top_k
        )
        
        dense_search_params = {"metric_type": "IP"}
        dense_req = AnnSearchRequest(
            query_embeddings["dense"],
            "dense_vector", 
            dense_search_params, 
            limit=top_k
        )
        
        search_requests = [sparse_req, dense_req]
        
        # 如果提供了形状向量，添加形状搜索请求
        if query_shape_vector is not None:
            # 确保查询向量格式正确
            if hasattr(query_shape_vector, 'tolist'):
                query_shape_vector = [query_shape_vector.tolist()]
            else:
                query_shape_vector = [query_shape_vector]
            
            shape_search_params = {"metric_type": "IP"}
            shape_req = AnnSearchRequest(
                query_shape_vector,
                "shape_vector", 
                shape_search_params, 
                limit=top_k
            )
            search_requests.append(shape_req)
            logger.info("执行混合搜索 (文本 + 形状)")
        else:
            logger.info("未提供形状向量，仅执行文本搜索")
        
        # 执行混合搜索
        results = collection.hybrid_search(
            search_requests, 
            rerank=RRFRanker(weight=[0.35, 0.35, 0.3] if query_shape_vector is not None else None),
            limit=top_k, 
            output_fields=["pk", "description"]
        )
        
        # 如果启用了重排序，使用BGE重排序器进一步优化结果
        if self.use_reranker:
            # 提取原始结果信息
            original_results = []
            for hit in results[0]:
                original_results.append({
                    'id': hit.fields["pk"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            
            result_texts = [hit.fields["description"] for hit in results[0]]
            
            # 对结果重新排序
            reranked_results = self.rerank_function(query_text, result_texts, top_k=top_k)
            
            logger.info("使用BGE CrossEncoder模型重新排序结果")
            
            # 构建重排序后的结果格式
            formatted_results = []
            for rerank_result in reranked_results:
                # 找到对应的原始结果
                original_result = next(
                    (r for r in original_results if r['description'] == rerank_result.text),
                    None
                )
                
                formatted_results.append({
                    'id': original_result['id'] if original_result else 'unknown',
                    'description': rerank_result.text,
                    'score': rerank_result.score
                })
            
            return formatted_results
        else:
            # 返回原始搜索结果，格式化为统一格式
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    'id': hit.fields["pk"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            
            return formatted_results
    
    def release_collection(self, collection_name):
        """
        释放集合资源
        
        参数:
            collection_name: 集合名称
        """
        self.connect()
        collection = Collection(name=collection_name)
        collection.release()
        logger.info(f"已释放集合 '{collection_name}' 资源")
