#!/usr/bin/env python3
"""
测试几何和语义查询智能体
Test Geometric and Semantic Query Agent
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.agent.geometric_semantic_agent import GeometricSemanticAgent
from src.agent.data_models import GeometricQuery, SemanticQuery
from src.config import Config


async def test_semantic_search():
    """测试语义搜索功能"""
    print("=" * 50)
    print("测试语义搜索功能")
    print("=" * 50)
    
    try:
        # 初始化智能体
        agent = GeometricSemanticAgent()
        
        # 测试查询
        test_queries = [
            "螺栓连接件",
            "支撑结构",
            "传动齿轮",
            "密封圈组件",
            "轴承座",
            "外壳部件"
        ]
        
        for query_text in test_queries:
            print(f"\n查询: {query_text}")
            query = SemanticQuery(
                query_text=query_text,
                limit=5,
                similarity_threshold=0.7
            )
            
            result = await agent.semantic_search(query)
            
            if result.success:
                print(f"找到 {len(result.results)} 个结果:")
                for i, item in enumerate(result.results[:3], 1):
                    print(f"  {i}. ID: {item.get('id', 'N/A')}")
                    print(f"     分数: {item.get('score', 'N/A'):.4f}")
                    print(f"     描述: {item.get('description', 'N/A')[:100]}...")
            else:
                print(f"搜索失败: {result.error}")
            
            print("-" * 30)
            
    except Exception as e:
        print(f"测试语义搜索时发生错误: {e}")


async def test_geometric_search():
    """测试几何搜索功能"""
    print("=" * 50)
    print("测试几何搜索功能")
    print("=" * 50)
    
    try:
        # 初始化智能体
        agent = GeometricSemanticAgent()
        
        # 模拟几何特征向量（实际应用中这应该从图像或3D模型提取）
        import numpy as np
        mock_feature_vector = np.random.rand(512).tolist()
        
        print("使用模拟几何特征向量进行搜索...")
        query = GeometricQuery(
            feature_vector=mock_feature_vector,
            limit=5,
            similarity_threshold=0.8
        )
        
        result = await agent.geometric_search(query)
        
        if result.success:
            print(f"找到 {len(result.results)} 个几何相似的结果:")
            for i, item in enumerate(result.results[:3], 1):
                print(f"  {i}. ID: {item.get('id', 'N/A')}")
                print(f"     相似度分数: {item.get('score', 'N/A'):.4f}")
                print(f"     描述: {item.get('description', 'N/A')[:100]}...")
        else:
            print(f"几何搜索失败: {result.error}")
            
    except Exception as e:
        print(f"测试几何搜索时发生错误: {e}")


async def test_hybrid_search():
    """测试混合搜索功能"""
    print("=" * 50)
    print("测试混合搜索功能")
    print("=" * 50)
    
    try:
        # 初始化智能体
        agent = GeometricSemanticAgent()
        
        # 创建混合查询
        import numpy as np
        mock_feature_vector = np.random.rand(512).tolist()
        
        query_text = "圆形螺栓连接"
        print(f"执行混合搜索: '{query_text}' + 几何特征")
        
        # 分别执行语义和几何搜索
        semantic_query = SemanticQuery(
            query_text=query_text,
            limit=10,
            similarity_threshold=0.6
        )
        
        geometric_query = GeometricQuery(
            feature_vector=mock_feature_vector,
            limit=10,
            similarity_threshold=0.7
        )
        
        # 并行执行两个搜索
        semantic_result, geometric_result = await asyncio.gather(
            agent.semantic_search(semantic_query),
            agent.geometric_search(geometric_query)
        )
        
        print(f"语义搜索结果: {len(semantic_result.results) if semantic_result.success else 0} 个")
        print(f"几何搜索结果: {len(geometric_result.results) if geometric_result.success else 0} 个")
        
        # 这里可以实现结果融合逻辑
        if semantic_result.success and geometric_result.success:
            print("混合搜索成功完成")
        else:
            print("部分搜索失败")
            
    except Exception as e:
        print(f"测试混合搜索时发生错误: {e}")


async def test_connection():
    """测试Milvus连接"""
    print("=" * 50)
    print("测试Milvus连接")
    print("=" * 50)
    
    try:
        agent = GeometricSemanticAgent()
        
        # 测试连接
        if hasattr(agent, 'milvus_client'):
            print("Milvus客户端已初始化")
            # 这里可以添加更多连接测试
        else:
            print("Milvus客户端未正确初始化")
            
    except Exception as e:
        print(f"测试连接时发生错误: {e}")


async def main():
    """主测试函数"""
    print("开始测试几何和语义查询智能体")
    print("=" * 60)
    
    # 按顺序执行测试
    await test_connection()
    await test_semantic_search()
    await test_geometric_search()
    await test_hybrid_search()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    # 检查配置
    try:
        config = Config()
        print(f"配置加载成功")
        print(f"Milvus配置: {config.milvus if hasattr(config, 'milvus') else '未找到'}")
    except Exception as e:
        print(f"配置加载失败: {e}")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
