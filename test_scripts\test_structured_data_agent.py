#!/usr/bin/env python3
"""
测试结构化数据查询智能体
Test Structured Data Query Agent
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.data_models import StructuredQuery
from src.config import Config


async def test_basic_sql_generation():
    """测试基本SQL生成功能"""
    print("=" * 50)
    print("测试基本SQL生成功能")
    print("=" * 50)
    
    try:
        # 初始化智能体
        agent = StructuredDataAgent()
        
        # 测试查询
        test_queries = [
            "查找所有质量大于100克的零件",
            "找出所有航空航天行业的装配体",
            "查询孔径标准差小于0.1的零件",
            "查找包含超过50个零件的装配体",
            "查询表面积在100到500平方毫米之间的零件",
            "找出所有钢材质的零件"
        ]
        
        for query_text in test_queries:
            print(f"\n查询: {query_text}")
            query = StructuredQuery(
                query_text=query_text,
                table_name="parts",  # 假设表名
                limit=10
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的SQL: {result.sql_query}")
                print(f"查询结果数量: {len(result.results)}")
                if result.results:
                    print(f"示例结果: {result.results[0] if result.results else 'None'}")
            else:
                print(f"查询失败: {result.error}")
            
            print("-" * 30)
            
    except Exception as e:
        print(f"测试SQL生成时发生错误: {e}")


async def test_complex_queries():
    """测试复杂查询"""
    print("=" * 50)
    print("测试复杂查询")
    print("=" * 50)
    
    try:
        agent = StructuredDataAgent()
        
        complex_queries = [
            {
                "query": "查找质量在50-200克之间，且表面积大于100平方毫米的铝合金零件",
                "table": "parts"
            },
            {
                "query": "统计每个行业类型的装配体数量",
                "table": "assemblies"
            },
            {
                "query": "找出零件数量最多的前5个装配体",
                "table": "assemblies"
            },
            {
                "query": "查询包含特定材质且孔径数量大于10的零件",
                "table": "parts"
            }
        ]
        
        for item in complex_queries:
            print(f"\n复杂查询: {item['query']}")
            query = StructuredQuery(
                query_text=item['query'],
                table_name=item['table'],
                limit=5
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的SQL: {result.sql_query}")
                print(f"执行状态: {'成功' if result.results is not None else '失败'}")
                if result.results:
                    print(f"结果数量: {len(result.results)}")
            else:
                print(f"查询失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试复杂查询时发生错误: {e}")


async def test_filtering_conditions():
    """测试过滤条件"""
    print("=" * 50)
    print("测试过滤条件")
    print("=" * 50)
    
    try:
        agent = StructuredDataAgent()
        
        filter_tests = [
            {
                "query": "查找质量大于阈值的零件",
                "filters": {"mass": {"min": 100}},
                "table": "parts"
            },
            {
                "query": "查询特定行业的装配体",
                "filters": {"industry_type": "aerospace"},
                "table": "assemblies"
            },
            {
                "query": "找出表面积在范围内的零件",
                "filters": {"surface_area": {"min": 50, "max": 200}},
                "table": "parts"
            }
        ]
        
        for test in filter_tests:
            print(f"\n过滤查询: {test['query']}")
            query = StructuredQuery(
                query_text=test['query'],
                table_name=test['table'],
                filters=test['filters'],
                limit=5
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"应用过滤条件: {test['filters']}")
                print(f"生成的SQL: {result.sql_query}")
                print(f"结果数量: {len(result.results) if result.results else 0}")
            else:
                print(f"过滤查询失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试过滤条件时发生错误: {e}")


async def test_database_schema():
    """测试数据库模式获取"""
    print("=" * 50)
    print("测试数据库模式获取")
    print("=" * 50)
    
    try:
        agent = StructuredDataAgent()
        
        # 获取表结构信息
        if hasattr(agent, 'get_table_schema'):
            test_tables = ['parts', 'assemblies', 'materials']
            
            for table_name in test_tables:
                print(f"\n获取表 '{table_name}' 的结构:")
                try:
                    schema = await agent.get_table_schema(table_name)
                    if schema:
                        print(f"表结构: {schema}")
                    else:
                        print(f"未找到表 '{table_name}' 的结构")
                except Exception as e:
                    print(f"获取表结构失败: {e}")
        else:
            print("智能体不支持获取表结构功能")
            
    except Exception as e:
        print(f"测试数据库模式时发生错误: {e}")


async def test_connection():
    """测试PostgreSQL连接"""
    print("=" * 50)
    print("测试PostgreSQL连接")
    print("=" * 50)
    
    try:
        agent = StructuredDataAgent()
        
        # 测试连接
        if hasattr(agent, 'postgres_client') or hasattr(agent, 'db_client'):
            print("PostgreSQL客户端已初始化")
            
            # 尝试执行简单查询
            test_query = StructuredQuery(
                query_text="获取数据库版本",
                table_name="",
                limit=1
            )
            
            # 这里可以添加更具体的连接测试
            print("连接测试: 基本连接正常")
        else:
            print("PostgreSQL客户端未正确初始化")
            
    except Exception as e:
        print(f"测试连接时发生错误: {e}")


async def test_sql_validation():
    """测试SQL验证"""
    print("=" * 50)
    print("测试SQL验证")
    print("=" * 50)
    
    try:
        agent = StructuredDataAgent()
        
        # 测试不同类型的SQL生成
        validation_tests = [
            "SELECT查询测试",
            "COUNT聚合查询测试", 
            "WHERE条件查询测试",
            "ORDER BY排序查询测试",
            "GROUP BY分组查询测试"
        ]
        
        for test_desc in validation_tests:
            print(f"\n{test_desc}:")
            query = StructuredQuery(
                query_text=test_desc,
                table_name="parts",
                limit=5
            )
            
            # 生成SQL但不执行
            if hasattr(agent, 'generate_sql'):
                try:
                    sql = await agent.generate_sql(query)
                    print(f"生成的SQL: {sql}")
                    print(f"SQL长度: {len(sql) if sql else 0} 字符")
                except Exception as e:
                    print(f"SQL生成失败: {e}")
            else:
                print("智能体不支持独立的SQL生成功能")
            
    except Exception as e:
        print(f"测试SQL验证时发生错误: {e}")


async def main():
    """主测试函数"""
    print("开始测试结构化数据查询智能体")
    print("=" * 60)
    
    # 按顺序执行测试
    await test_connection()
    await test_basic_sql_generation()
    await test_complex_queries()
    await test_filtering_conditions()
    await test_database_schema()
    await test_sql_validation()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    # 检查配置
    try:
        config = Config()
        print(f"配置加载成功")
        print(f"数据库配置: {config.database if hasattr(config, 'database') else '未找到'}")
    except Exception as e:
        print(f"配置加载失败: {e}")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
