"""
测试 Text2Cypher Agent
"""

import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.text2cypher_agent import Text2CypherAgent, create_text2cypher_agent


def test_text2cypher_agent():
    """测试Text2Cypher Agent功能"""
    
    print("创建 Text2Cypher Agent...")
    agent = create_text2cypher_agent()
    
    # 测试问题集
    test_questions = [
        "重量大于30的装配体",
    ]
    print(f"\n开始测试 {len(test_questions)} 个问题:")
    print("=" * 80)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n[测试 {i}]")
        print(f"问题: {question}")
        
        try:
            result = agent.chat(question)
            print(f"状态: {result['status']}")
            print(f"生成的Cypher查询:")
            print(f"```cypher")
            print(f"{result['cypher_query']}")
            print(f"```")
            
            # 显示查询结果
            if result['status'] == 'success':
                print(f"查询结果: \n {result['query_result']}")

            else:
                print(f"错误信息: {result.get('error_message', '未知错误')}")
            
        except Exception as e:
            print(f"执行错误: {str(e)}")
        
        print("-" * 60)
    
    print("\n批量测试...")
    try:
        batch_results = agent.batch_generate(test_questions[:3])
        print(f"批量处理了 {len(batch_results)} 个问题")
        for result in batch_results:
            print(f"- {result['question']}: {result['status']}")
    except Exception as e:
        print(f"批量测试错误: {str(e)}")


def test_custom_llm():
    """测试自定义LLM配置"""
    
    print("\n测试自定义LLM配置...")
    
    try:
        from src.models.LLM import MultiProviderLLM
        
        # 创建自定义LLM
        custom_llm = MultiProviderLLM(
            provider="openai",
            api_url="http://**************:4000/",
            api_key="sk-YyiBg6DSn1Fc2KBNU6ZYtw",
            model="DeepSeek-V3-0324",
        )
        
        # 使用自定义LLM创建agent
        agent = Text2CypherAgent(llm=custom_llm)
        
        # 测试简单问题
        result = agent.chat("有多少个零件?")
        print(f"自定义LLM测试成功")
        print(f"问题: {result['question']}")
        print(f"查询: {result['cypher_query']}")
        print(f"状态: {result['status']}")
        
    except Exception as e:
        print(f"自定义LLM测试失败: {str(e)}")


if __name__ == "__main__":
    print("Text2Cypher Agent 测试脚本")
    print("=" * 50)
    
    # 基本功能测试
    test_text2cypher_agent()
    
    # 自定义LLM测试
    test_custom_llm()
    
    print("\n测试完成!")
