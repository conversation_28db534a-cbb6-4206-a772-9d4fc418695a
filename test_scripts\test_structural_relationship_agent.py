#!/usr/bin/env python3
"""
测试结构关系查询智能体
Test Structural Relationship Query Agent
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.data_models import StructuralQuery
from src.config import Config


async def test_basic_cypher_generation():
    """测试基本Cypher生成功能"""
    print("=" * 50)
    print("测试基本Cypher生成功能")
    print("=" * 50)
    
    try:
        # 初始化智能体
        agent = StructuralRelationshipAgent()
        
        # 测试查询
        test_queries = [
            "查找零件Base_Top的所有父装配体",
            "找出通过螺栓连接的所有零件对",
            "查找包含Under Barrel组件的的装配体",
        ]
        
        for query_text in test_queries:
            print(f"\n查询: {query_text}")
            query = StructuralQuery(
                query_text=query_text,
                node_type="Assembly",
                limit=10
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的Cypher: {result.cypher_query}")
                print(f"查询结果数量: {len(result.results)}")
                if result.results:
                    print(f"示例结果: {result.results[0] if result.results else 'None'}")
            else:
                print(f"查询失败: {result.error}")
            
            print("-" * 30)
            
    except Exception as e:
        print(f"测试Cypher生成时发生错误: {e}")


async def test_relationship_queries():
    """测试关系查询"""
    print("=" * 50)
    print("测试关系查询")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        relationship_queries = [
            {
                "query": "找出所有父子关系的零件对",
                "relationship": "CONTAINS"
            },
            {
                "query": "查询所有连接关系",
                "relationship": "CONNECTED_TO"
            },
            {
                "query": "找出装配关系",
                "relationship": "ASSEMBLED_WITH"
            },
            {
                "query": "查询支撑关系",
                "relationship": "SUPPORTS"
            }
        ]
        
        for item in relationship_queries:
            print(f"\n关系查询: {item['query']}")
            query = StructuralQuery(
                query_text=item['query'],
                relationship_type=item['relationship'],
                limit=5
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的Cypher: {result.cypher_query}")
                print(f"执行状态: {'成功' if result.results is not None else '失败'}")
                if result.results:
                    print(f"结果数量: {len(result.results)}")
            else:
                print(f"查询失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试关系查询时发生错误: {e}")


async def test_path_queries():
    """测试路径查询"""
    print("=" * 50)
    print("测试路径查询")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        path_queries = [
            {
                "query": "查找从零件A到零件B的最短路径",
                "start_node": "part_a",
                "end_node": "part_b"
            },
            {
                "query": "找出长度为2的所有路径",
                "path_length": 2
            },
            {
                "query": "查询从根装配体到叶子零件的路径",
                "start_node": "root_assembly"
            }
        ]
        
        for item in path_queries:
            print(f"\n路径查询: {item['query']}")
            query = StructuralQuery(
                query_text=item['query'],
                start_node_id=item.get('start_node'),
                end_node_id=item.get('end_node'),
                max_depth=item.get('path_length', 5),
                limit=5
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的Cypher: {result.cypher_query}")
                print(f"路径查询结果数量: {len(result.results) if result.results else 0}")
            else:
                print(f"路径查询失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试路径查询时发生错误: {e}")


async def test_aggregation_queries():
    """测试聚合查询"""
    print("=" * 50)
    print("测试聚合查询")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        aggregation_queries = [
            "统计每个装配体包含的零件数量",
            "计算图中节点的总数",
            "找出度数最高的节点",
            "统计不同类型关系的数量",
            "计算平均装配深度"
        ]
        
        for query_text in aggregation_queries:
            print(f"\n聚合查询: {query_text}")
            query = StructuralQuery(
                query_text=query_text,
                limit=10
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的Cypher: {result.cypher_query}")
                if result.results:
                    print(f"聚合结果: {result.results[0] if result.results else 'None'}")
            else:
                print(f"聚合查询失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试聚合查询时发生错误: {e}")


async def test_node_pattern_matching():
    """测试节点模式匹配"""
    print("=" * 50)
    print("测试节点模式匹配")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        pattern_queries = [
            {
                "query": "找出所有Part类型的节点",
                "node_type": "Part"
            },
            {
                "query": "查询所有Assembly类型的节点", 
                "node_type": "Assembly"
            },
            {
                "query": "找出具有特定属性的节点",
                "properties": {"material": "steel"}
            },
            {
                "query": "查询名称包含'gear'的零件",
                "properties": {"name": "*gear*"}
            }
        ]
        
        for item in pattern_queries:
            print(f"\n模式匹配: {item['query']}")
            query = StructuralQuery(
                query_text=item['query'],
                node_type=item.get('node_type'),
                properties=item.get('properties'),
                limit=5
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"生成的Cypher: {result.cypher_query}")
                print(f"匹配结果数量: {len(result.results) if result.results else 0}")
            else:
                print(f"模式匹配失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试节点模式匹配时发生错误: {e}")


async def test_connection():
    """测试Neo4j连接"""
    print("=" * 50)
    print("测试Neo4j连接")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        # 测试连接
        if hasattr(agent, 'neo4j_client') or hasattr(agent, 'driver'):
            print("Neo4j客户端已初始化")
            
            # 尝试执行简单查询
            test_query = StructuralQuery(
                query_text="获取数据库信息",
                limit=1
            )
            
            # 这里可以添加更具体的连接测试
            print("连接测试: 基本连接正常")
        else:
            print("Neo4j客户端未正确初始化")
            
    except Exception as e:
        print(f"测试连接时发生错误: {e}")


async def test_cypher_validation():
    """测试Cypher验证"""
    print("=" * 50)
    print("测试Cypher验证")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        # 测试不同类型的Cypher生成
        validation_tests = [
            "MATCH查询测试",
            "WHERE条件查询测试",
            "RETURN结果查询测试",
            "ORDER BY排序查询测试",
            "WITH子句查询测试"
        ]
        
        for test_desc in validation_tests:
            print(f"\n{test_desc}:")
            query = StructuralQuery(
                query_text=test_desc,
                limit=5
            )
            
            # 生成Cypher但不执行
            if hasattr(agent, 'generate_cypher'):
                try:
                    cypher = await agent.generate_cypher(query)
                    print(f"生成的Cypher: {cypher}")
                    print(f"Cypher长度: {len(cypher) if cypher else 0} 字符")
                except Exception as e:
                    print(f"Cypher生成失败: {e}")
            else:
                print("智能体不支持独立的Cypher生成功能")
            
    except Exception as e:
        print(f"测试Cypher验证时发生错误: {e}")


async def test_graph_analysis():
    """测试图分析功能"""
    print("=" * 50)
    print("测试图分析功能")
    print("=" * 50)
    
    try:
        agent = StructuralRelationshipAgent()
        
        analysis_queries = [
            "分析装配体的层次深度",
            "查找图中的强连通分量",
            "检测环形依赖关系",
            "计算节点的中心性",
            "找出孤立的节点"
        ]
        
        for query_text in analysis_queries:
            print(f"\n图分析: {query_text}")
            query = StructuralQuery(
                query_text=query_text,
                limit=10
            )
            
            result = await agent.execute_query(query)
            
            if result.success:
                print(f"分析查询: {result.cypher_query}")
                print(f"分析结果: {'有结果' if result.results else '无结果'}")
            else:
                print(f"图分析失败: {result.error}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"测试图分析时发生错误: {e}")


async def main():
    """主测试函数"""
    print("开始测试结构关系查询智能体")
    print("=" * 60)
    
    # 按顺序执行测试
    await test_connection()
    await test_basic_cypher_generation()
    await test_relationship_queries()
    await test_path_queries()
    await test_aggregation_queries()
    await test_node_pattern_matching()
    await test_cypher_validation()
    await test_graph_analysis()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    # 检查配置
    try:
        config = Config()
        print(f"配置加载成功")
        print(f"Neo4j配置: {config.neo4j if hasattr(config, 'neo4j') else '未找到'}")
    except Exception as e:
        print(f"配置加载失败: {e}")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
