# A demo showing hybrid semantic search with dense and sparse vectors using Milvus.
# 这是一个使用Milvus进行密集向量和稀疏向量混合语义搜索的演示。
#
# You can optionally choose to use the BGE-M3 model to embed the text as dense
# and sparse vectors, or simply use random generated vectors as an example.
# 您可以选择使用BGE-M3模型将文本嵌入为密集向量和稀疏向量，
# 或者简单地使用随机生成的向量作为示例。
#
# You can also use the BGE CrossEncoder model to rerank the search results.
# 您还可以使用BGE CrossEncoder模型对搜索结果进行重新排序。
#
# Note that the sparse vector search feature is only available in Milvus 2.4.0 or
# higher version. Make sure you follow https://milvus.io/docs/install_standalone-docker.md
# to set up the latest version of Milvus in your local environment.
# 注意，稀疏向量搜索功能仅在Milvus 2.4.0或更高版本中可用。
# 请确保按照https://milvus.io/docs/install_standalone-docker.md的指引
# 在本地环境中设置最新版本的Milvus。

# To connect to Milvus server, you need the python client library called pymilvus.
# To use BGE-M3 model, you need to install the optional `model` module in pymilvus.
# You can get them by simply running the following commands:
# 要连接到Milvus服务器，您需要名为pymilvus的Python客户端库。
# 要使用BGE-M3模型，您需要安装pymilvus中的可选`model`模块。
# 您可以通过运行以下命令来获取它们：
#
# pip install pymilvus
# pip install pymilvus[model]

# If true, use BGE-M3 model to generate dense and sparse vectors.
# If false, use random numbers to compose dense and sparse vectors.
# 如果为true，则使用BGE-M3模型生成密集和稀疏向量。
# 如果为false，则使用随机数组成密集和稀疏向量。
use_bge_m3 = True
# If true, the search result will be reranked using BGE CrossEncoder model.
# 如果为true，搜索结果将使用BGE CrossEncoder模型重新排序。
use_reranker = True  # 禁用重排序功能

# The overall steps are as follows:
# 1. embed the text as dense and sparse vectors
# 2. setup a Milvus collection to store the dense and sparse vectors
# 3. insert the data to Milvus
# 4. search and inspect the result!
# 整体步骤如下：
# 1. 将文本嵌入为密集和稀疏向量
# 2. 设置Milvus集合以存储密集和稀疏向量
# 3. 将数据插入Milvus
# 4. 搜索并检查结果！
import random
import string
import numpy as np

from pymilvus import (
    utility,
    FieldSchema, CollectionSchema, DataType,
    Collection, AnnSearchRequest, RRFRanker, connections,
)

# 1. prepare a small corpus to search
# 1. 准备一个小型语料库进行搜索

assembly_1 = """
电子连接器外壳；这是一款用于电子设备内部的连接器外壳，主要作用是固定和保护连接器插针，并提供可靠的电气连接。外壳整体呈长方体结构，表面平整，具有多个安装孔和插针
固定槽。；组件：；外壳本体：长方体结构，提供整体支撑和保护，四个安装孔用于固定连接器；插针：金属圆柱形，用于电气连接，共四个，均匀分布在壳体内部；材料工艺：该连
接器外壳主要采用钢材制造，具有较高的强度和耐用性。可能经过表面处理工艺（如喷涂或电镀）以增强防腐蚀性能和绝缘性。
"""
assembly_2 = """
汽车轮毂；这款产品是汽车轮毂，用于连接轮胎并固定在车辆轴上，支撑车身重量并实现车辆的行驶。整体呈圆形结构，外圈设有橡胶轮胎，内部为金属轮组。；组件：；轮组：由钢
材制成，是轮胎的支撑结构，具有多个辐条设计以增强强度和稳定性。；轮胎：外圈橡胶材质，提供与路面的摩擦力，减震和缓冲作用。；材料工艺：主要材料为钢材和橡胶，轮组采
用金属铸造或锻造工艺保证强度，轮胎则通过硫化等橡胶加工技术成型。整体设计注重结构强度和行驶安全性。
"""

assembly_3 = """
管状物批量处理设备；这是一款用于批量处理细长管状物的自动化设备。它主要由一个带有多个孔洞的机身和控制面板组成，用于固定、支撑或输送管状物。设备整体呈长方体结构，
设计紧凑实用。；组件：；机身：设备的主体结构，提供多个孔洞用于固定和支撑管状物。；控制面板：位于机身前端，用于操作和监控设备运行状态。；孔洞阵列：机身顶部的大量
圆孔，用于同时容纳和固定多个管状物。；支撑底座：位于机身底部，提供设备的稳定支撑。；控制按钮：位于控制面板上，用于启动、停止和调节设备参数。；材料工艺：该设备主
要采用钢材制造，具有较高的强度和耐用性。机身可能经过表面处理工艺（如喷涂）以防止锈蚀和提升美观度。孔洞的精确加工保证了管状物的稳定固定。
"""



docs = [
    assembly_1,
    assembly_2,
    assembly_3,
]
query = "用于输送管状物的设备"

import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'


# BGE-M3模型可以将文本嵌入为密集和稀疏向量。
# 它包含在pymilvus的可选`model`模块中，要安装它，
# 只需运行"pip install pymilvus[model]"。
from pymilvus.model.hybrid import BGEM3EmbeddingFunction
model_path = r"C:\Users\<USER>\.cache\huggingface\hub\models--BAAI--bge-m3\snapshots\5617a9f61b028005a4858fdac845db406aefb181"
ef = BGEM3EmbeddingFunction(model_name=model_path,use_fp16=False, device="cpu")  # 使用CPU设备初始化BGE-M3嵌入函数
dense_dim = ef.dim["dense"]  # 获取BGE-M3模型的密集向量维度

# 对文档和查询进行嵌入
docs_embeddings = ef(docs)  # 生成文档的嵌入向量
query_embeddings = ef([query])  # 生成查询的嵌入向量

# 2. setup Milvus collection and index
# 2. 设置Milvus集合和索引
connections.connect("default", host="localhost", port="19530")  # 连接到本地Milvus服务器

# 先删除同名集合（如果存在）
col_name = 'hybrid_demo'  # 集合名称
if utility.has_collection(col_name):
    utility.drop_collection(col_name)

# Specify the data schema for the new Collection.
# 为新集合指定数据模式
fields = [
    # Use auto generated id as primary key
    # 使用自动生成的ID作为主键
    FieldSchema(name="pk", dtype=DataType.VARCHAR,
                is_primary=True, auto_id=True, max_length=100),
    # Store the original text to retrieve based on semantically distance
    # 存储原始文本，以便基于语义距离进行检索
    FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=1024),
    # Milvus now supports both sparse and dense vectors, we can store each in
    # a separate field to conduct hybrid search on both vectors.
    # Milvus现在同时支持稀疏和密集向量，我们可以将每种向量存储在
    # 单独的字段中，以便对两种向量进行混合搜索。
    FieldSchema(name="sparse_vector", dtype=DataType.SPARSE_FLOAT_VECTOR),  # 稀疏向量字段
    FieldSchema(name="dense_vector", dtype=DataType.FLOAT_VECTOR,
                dim=dense_dim),  # 密集向量字段，维度为dense_dim
]
schema = CollectionSchema(fields, "")  # 创建集合模式
# Now we can create the new collection with above name and schema.
# 现在我们可以使用上述名称和模式创建新集合。
col = Collection(col_name, schema, consistency_level="Strong")  # 创建强一致性级别的集合

# We need to create indices for the vector fields. The indices will be loaded
# into memory for efficient search.
# 我们需要为向量字段创建索引。这些索引将被加载到内存中以进行高效搜索。
sparse_index = {"index_type": "SPARSE_INVERTED_INDEX", "metric_type": "IP"}  # 稀疏向量使用倒排索引
col.create_index("sparse_vector", sparse_index)  # 为稀疏向量创建索引
dense_index = {"index_type": "FLAT", "metric_type": "IP"}  # 密集向量使用FLAT索引
col.create_index("dense_vector", dense_index)  # 为密集向量创建索引
col.load()  # 将索引加载到内存中

# 3. insert text and sparse/dense vector representations into the collection
# 3. 将文本和稀疏/密集向量表示插入集合
entities = [docs, docs_embeddings["sparse"], docs_embeddings["dense"]]  # 准备要插入的实体数据
col.insert(entities)  # 插入数据
col.flush()  # 确保数据持久化

# 4. search and inspect the result!
# 4. 搜索并检查结果！
k = 2  # we want to get the top 2 docs closest to the query
       # 我们想要获取与查询最接近的前2个文档

# Prepare the search requests for both vector fields
# 为两个向量字段准备搜索请求
sparse_search_params = {"metric_type": "IP"}  # 稀疏向量使用内积相似度
sparse_req = AnnSearchRequest(query_embeddings["sparse"],
                              "sparse_vector", sparse_search_params, limit=k)  # 稀疏向量搜索请求
dense_search_params = {"metric_type": "IP"}  # 密集向量使用内积相似度
dense_req = AnnSearchRequest(query_embeddings["dense"],
                             "dense_vector", dense_search_params, limit=k)  # 密集向量搜索请求

# Search topK docs based on dense and sparse vectors and rerank with RRF.
# 基于密集和稀疏向量搜索前K个文档，并使用RRF（Reciprocal Rank Fusion）进行重排序
res = col.hybrid_search([sparse_req, dense_req], rerank=RRFRanker(),
                        limit=k, output_fields=['text'])  # 混合搜索请求


# 目前Milvus在同一个混合搜索请求中只支持1个查询，所以我们直接检查res[0]。
# 在未来的版本中，Milvus将接受批量混合搜索查询在同一个调用中。
res = res[0]  # 获取第一个查询的结果

if use_reranker:
    # 如果使用重排序器
    result_texts = [hit.fields["text"] for hit in res]  # 提取搜索结果中的文本
    from pymilvus.model.reranker import BGERerankFunction
    bge_rf = BGERerankFunction(device='cpu')  # 初始化BGE重排序函数
    # rerank the results using BGE CrossEncoder model
    # 使用BGE CrossEncoder模型重新排序结果
    results = bge_rf(query, result_texts, top_k=2)  # 对结果重新排序
    for hit in results:
        print(f'text: {hit.text} distance {hit.score}')  # 打印重排序后的结果和分数
else:
    # 如果不使用重排序器
    for hit in res:
        print(f'text: {hit.fields["text"]} distance {hit.distance}')  # 打印原始搜索结果和距离


# 如果您同时使用了BGE-M3和重排序器，您应该看到以下结果：
# text: Alan Turing was the first person to conduct substantial research in AI. distance 0.9306981017573297
# text: Artificial intelligence was founded as an academic discipline in 1956. distance 0.03217001154515051
#
# 如果您只使用了BGE-M3，您应该看到以下结果：
# text: Alan Turing was the first person to conduct substantial research in AI. distance 0.032786883413791656
# text: Artificial intelligence was founded as an academic discipline in 1956. distance 0.016129031777381897

# In this simple example the reranker yields the same result as the embedding based hybrid search, but in more complex
# scenarios the reranker can provide more accurate results.
# 在这个简单的例子中，重排序器产生的结果与基于嵌入的混合搜索相同，但在更复杂的
# 场景中，重排序器可以提供更准确的结果。

# If you used random vectors, the result will be different each time you run the script.
# 如果您使用了随机向量，每次运行脚本时结果都会不同。

# Drop the collection to clean up the data.
# 删除集合以清理数据。
utility.drop_collection(col_name)