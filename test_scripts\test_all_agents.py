#!/usr/bin/env python3
"""
综合测试所有查询智能体
Comprehensive Test for All Query Agents
"""

import sys
import os
import asyncio
from pathlib import Path
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.agent.geometric_semantic_agent import GeometricSemanticAgent
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.data_models import GeometricQuery, SemanticQuery, StructuredQuery, StructuralQuery
from src.config import Config


class AgentTestSuite:
    """智能体测试套件"""
    
    def __init__(self):
        self.geometric_agent = None
        self.structured_agent = None
        self.structural_agent = None
        self.test_results = {
            'geometric': [],
            'structured': [],
            'structural': []
        }
    
    async def initialize_agents(self):
        """初始化所有智能体"""
        print("正在初始化智能体...")
        
        try:
            self.geometric_agent = GeometricSemanticAgent()
            print("✓ 几何语义智能体初始化成功")
        except Exception as e:
            print(f"✗ 几何语义智能体初始化失败: {e}")
        
        try:
            self.structured_agent = StructuredDataAgent()
            print("✓ 结构化数据智能体初始化成功")
        except Exception as e:
            print(f"✗ 结构化数据智能体初始化失败: {e}")
        
        try:
            self.structural_agent = StructuralRelationshipAgent()
            print("✓ 结构关系智能体初始化成功")
        except Exception as e:
            print(f"✗ 结构关系智能体初始化失败: {e}")
    
    async def test_geometric_agent(self):
        """测试几何语义智能体"""
        print("\n" + "="*50)
        print("测试几何语义智能体")
        print("="*50)
        
        if not self.geometric_agent:
            print("几何语义智能体未初始化，跳过测试")
            return
        
        # 语义搜索测试
        semantic_tests = [
            "螺栓连接件",
            "传动齿轮", 
            "支撑结构"
        ]
        
        for query_text in semantic_tests:
            start_time = time.time()
            try:
                query = SemanticQuery(
                    query_text=query_text,
                    limit=3,
                    similarity_threshold=0.7
                )
                result = await self.geometric_agent.semantic_search(query)
                
                elapsed = time.time() - start_time
                success = result.success if result else False
                
                self.test_results['geometric'].append({
                    'type': 'semantic',
                    'query': query_text,
                    'success': success,
                    'elapsed_time': elapsed,
                    'result_count': len(result.results) if result and result.results else 0
                })
                
                print(f"语义搜索 '{query_text}': {'成功' if success else '失败'} ({elapsed:.2f}s)")
                
            except Exception as e:
                print(f"语义搜索 '{query_text}' 异常: {e}")
        
        # 几何搜索测试
        try:
            import numpy as np
            mock_vector = np.random.rand(512).tolist()
            
            start_time = time.time()
            query = GeometricQuery(
                feature_vector=mock_vector,
                limit=3,
                similarity_threshold=0.8
            )
            result = await self.geometric_agent.geometric_search(query)
            
            elapsed = time.time() - start_time
            success = result.success if result else False
            
            self.test_results['geometric'].append({
                'type': 'geometric',
                'query': 'mock_vector_search',
                'success': success,
                'elapsed_time': elapsed,
                'result_count': len(result.results) if result and result.results else 0
            })
            
            print(f"几何搜索: {'成功' if success else '失败'} ({elapsed:.2f}s)")
            
        except Exception as e:
            print(f"几何搜索异常: {e}")
    
    async def test_structured_agent(self):
        """测试结构化数据智能体"""
        print("\n" + "="*50)
        print("测试结构化数据智能体")
        print("="*50)
        
        if not self.structured_agent:
            print("结构化数据智能体未初始化，跳过测试")
            return
        
        sql_tests = [
            {
                "query": "查找质量大于100克的零件",
                "table": "parts"
            },
            {
                "query": "统计每个行业的装配体数量",
                "table": "assemblies"
            },
            {
                "query": "查询表面积在100-500平方毫米的零件",
                "table": "parts"
            }
        ]
        
        for test in sql_tests:
            start_time = time.time()
            try:
                query = StructuredQuery(
                    query_text=test['query'],
                    table_name=test['table'],
                    limit=5
                )
                result = await self.structured_agent.execute_query(query)
                
                elapsed = time.time() - start_time
                success = result.success if result else False
                
                self.test_results['structured'].append({
                    'type': 'sql',
                    'query': test['query'],
                    'success': success,
                    'elapsed_time': elapsed,
                    'result_count': len(result.results) if result and result.results else 0
                })
                
                print(f"SQL查询 '{test['query'][:30]}...': {'成功' if success else '失败'} ({elapsed:.2f}s)")
                if success and result.sql_query:
                    print(f"  生成SQL: {result.sql_query[:80]}...")
                
            except Exception as e:
                print(f"SQL查询 '{test['query']}' 异常: {e}")
    
    async def test_structural_agent(self):
        """测试结构关系智能体"""
        print("\n" + "="*50)
        print("测试结构关系智能体")
        print("="*50)
        
        if not self.structural_agent:
            print("结构关系智能体未初始化，跳过测试")
            return
        
        cypher_tests = [
            {
                "query": "查找零件A的所有父装配体",
                "node_type": "Part"
            },
            {
                "query": "找出通过螺栓连接的零件对",
                "relationship": "CONNECTED_TO"
            },
            {
                "query": "统计每个装配体的零件数量",
                "node_type": "Assembly"
            }
        ]
        
        for test in cypher_tests:
            start_time = time.time()
            try:
                query = StructuralQuery(
                    query_text=test['query'],
                    node_type=test.get('node_type'),
                    relationship_type=test.get('relationship'),
                    limit=5
                )
                result = await self.structural_agent.execute_query(query)
                
                elapsed = time.time() - start_time
                success = result.success if result else False
                
                self.test_results['structural'].append({
                    'type': 'cypher',
                    'query': test['query'],
                    'success': success,
                    'elapsed_time': elapsed,
                    'result_count': len(result.results) if result and result.results else 0
                })
                
                print(f"Cypher查询 '{test['query'][:30]}...': {'成功' if success else '失败'} ({elapsed:.2f}s)")
                if success and result.cypher_query:
                    print(f"  生成Cypher: {result.cypher_query[:80]}...")
                
            except Exception as e:
                print(f"Cypher查询 '{test['query']}' 异常: {e}")
    
    async def test_integration_scenario(self):
        """测试集成场景"""
        print("\n" + "="*50)
        print("测试智能体集成场景")
        print("="*50)
        
        # 模拟一个复杂查询场景
        scenario = "在发动机装配体中找到与图片相似且重量小于200g的支撑零件"
        print(f"复杂查询场景: {scenario}")
        
        try:
            # 步骤1: 语义搜索找到支撑零件
            if self.geometric_agent:
                semantic_query = SemanticQuery(
                    query_text="支撑零件",
                    limit=10,
                    similarity_threshold=0.6
                )
                semantic_result = await self.geometric_agent.semantic_search(semantic_query)
                print(f"  步骤1 - 语义搜索: {'成功' if semantic_result.success else '失败'}")
            
            # 步骤2: 结构化查询过滤重量
            if self.structured_agent:
                structured_query = StructuredQuery(
                    query_text="查找重量小于200克的零件",
                    table_name="parts",
                    filters={"mass": {"max": 200}},
                    limit=10
                )
                structured_result = await self.structured_agent.execute_query(structured_query)
                print(f"  步骤2 - 结构化过滤: {'成功' if structured_result.success else '失败'}")
            
            # 步骤3: 结构关系查询装配体信息
            if self.structural_agent:
                structural_query = StructuralQuery(
                    query_text="查找发动机装配体中的零件",
                    node_type="Part",
                    limit=10
                )
                structural_result = await self.structural_agent.execute_query(structural_query)
                print(f"  步骤3 - 结构关系查询: {'成功' if structural_result.success else '失败'}")
            
            print("  集成场景测试完成")
            
        except Exception as e:
            print(f"集成场景测试异常: {e}")
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        
        for agent_type, results in self.test_results.items():
            if not results:
                continue
                
            total_tests = len(results)
            successful_tests = sum(1 for r in results if r['success'])
            total_time = sum(r['elapsed_time'] for r in results)
            avg_time = total_time / total_tests if total_tests > 0 else 0
            
            print(f"\n{agent_type.upper()} 智能体:")
            print(f"  总测试数: {total_tests}")
            print(f"  成功测试: {successful_tests}")
            print(f"  成功率: {successful_tests/total_tests*100:.1f}%")
            print(f"  平均耗时: {avg_time:.2f}s")
            print(f"  总耗时: {total_time:.2f}s")
            
            # 显示详细结果
            for result in results:
                status = "✓" if result['success'] else "✗"
                print(f"    {status} {result['type']}: {result['query'][:40]}... ({result['elapsed_time']:.2f}s)")


async def main():
    """主测试函数"""
    print("智能体综合测试套件")
    print("="*60)
    
    # 检查配置
    try:
        config = Config()
        print("✓ 配置加载成功")
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return
    
    # 创建测试套件
    test_suite = AgentTestSuite()
    
    # 初始化智能体
    await test_suite.initialize_agents()
    
    # 执行测试
    await test_suite.test_geometric_agent()
    await test_suite.test_structured_agent()
    await test_suite.test_structural_agent()
    await test_suite.test_integration_scenario()
    
    # 打印总结
    test_suite.print_summary()
    
    print("\n" + "="*60)
    print("所有测试完成")


if __name__ == "__main__":
    asyncio.run(main())
