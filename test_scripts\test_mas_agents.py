"""
多智能体系统使用示例
演示几何语义智能体、结构化数据智能体和结构关系智能体的使用
"""

import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.agent_manager import AgentManager
from src.agent.geometry_semantic_agent import GeometrySemanticAgent
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.structural_relationship_agent import StructuralRelationshipAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def demo_geometry_semantic_agent():
    """演示几何和语义查询智能体"""
    print("\n" + "="*50)
    print("演示几何和语义查询智能体")
    print("="*50)
    
    agent = GeometrySemanticAgent()
    
    try:
        await agent.connect()
        
        # 1. 文本语义搜索
        print("\n1. 文本语义搜索示例:")
        query_text = "圆柱形的金属零件"
        results = await agent.search_by_text(query_text, top_k=3)
        print(f"查询: {query_text}")
        print(f"结果数量: {len(results)}")
        for result in results:
            print(f"  - 排名 {result['rank']}: {result['assembly_id']} "
                  f"(相似度: {result['similarity_score']:.3f})")
            print(f"    描述: {result['description'][:100]}...")
        
        # 2. 形状几何搜索（使用示例向量）
        print("\n2. 形状几何搜索示例:")
        # 创建一个示例的768维向量（实际使用时应该是从3D模型提取的CLIP特征）
        import numpy as np
        shape_vector = np.random.rand(768).tolist()
        
        try:
            results = await agent.search_by_shape(shape_vector, top_k=3)
            print(f"查询向量维度: {len(shape_vector)}")
            print(f"结果数量: {len(results)}")
            for result in results:
                print(f"  - 排名 {result['rank']}: {result['assembly_id']} "
                      f"(相似度: {result['similarity_score']:.3f})")
        except Exception as e:
            print(f"形状搜索出错（可能是数据库中没有形状向量）: {e}")
        
        # 3. 混合搜索
        print("\n3. 混合搜索示例:")
        try:
            results = await agent.search_hybrid_with_shape(
                query_text="支架零件",
                shape_vector=shape_vector[:768],  # 确保768维
                top_k=3,
                shape_weight=0.3
            )
            print(f"文本查询: 支架零件")
            print(f"形状权重: 0.3")
            print(f"结果数量: {len(results)}")
            for result in results:
                print(f"  - 排名 {result['rank']}: {result['assembly_id']} "
                      f"(综合分数: {result['similarity_score']:.3f})")
        except Exception as e:
            print(f"混合搜索出错: {e}")
            
    except Exception as e:
        print(f"几何语义智能体演示失败: {e}")
    finally:
        await agent.disconnect()


async def demo_structured_data_agent():
    """演示结构化数据查询智能体"""
    print("\n" + "="*50)
    print("演示结构化数据查询智能体")
    print("="*50)
    
    agent = StructuredDataAgent()
    
    try:
        await agent.connect()
        
        # 1. 自然语言转SQL查询
        print("\n1. 自然语言转SQL查询示例:")
        queries = [
            "查找质量大于100克的零件",
            "找出所有钢材制作的零件",
            "显示有孔的零件，按体积排序"
        ]
        
        for query in queries:
            try:
                sql = await agent.text_to_sql(query)
                print(f"自然语言: {query}")
                print(f"生成的SQL: {sql}")
                
                # 执行查询
                results = await agent.query_by_text(query, generate_sql=True)
                print(f"结果数量: {len(results)}")
                
                # 显示前3个结果
                for i, result in enumerate(results[:3]):
                    print(f"  - 零件 {i+1}: {result.get('name', 'Unknown')} "
                          f"(质量: {result.get('mass', 'N/A')}g)")
                print()
                
            except Exception as e:
                print(f"查询 '{query}' 失败: {e}\n")
        
        # 2. 属性过滤查询
        print("2. 属性过滤查询示例:")
        try:
            results = await agent.query_parts_by_properties(
                material="steel",
                mass_min=10.0,
                mass_max=1000.0,
                has_holes=True,
                limit=5
            )
            print(f"过滤条件: 材料包含'steel', 质量10-1000g, 有孔")
            print(f"结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                print(f"  - 零件 {i+1}: {result.get('name', 'Unknown')}")
                print(f"    材料: {result.get('material', 'N/A')}")
                print(f"    质量: {result.get('mass', 'N/A')}g")
                print(f"    孔数量: {result.get('hole_count', 'N/A')}")
                
        except Exception as e:
            print(f"属性过滤查询失败: {e}")
            
    except Exception as e:
        print(f"结构化数据智能体演示失败: {e}")
    finally:
        await agent.disconnect()


async def demo_structural_relationship_agent():
    """演示结构关系智能体"""
    print("\n" + "="*50)
    print("演示结构关系智能体")
    print("="*50)
    
    agent = StructuralRelationshipAgent()
    
    try:
        await agent.connect()
        
        # 1. 自然语言转Cypher查询
        print("\n1. 自然语言转Cypher查询示例:")
        queries = [
            "找出所有装配体中的零件",
            "查找具有孔特征的零件",
            "显示装配体的层级结构"
        ]
        
        for query in queries:
            try:
                cypher = await agent.text_to_cypher(query)
                print(f"自然语言: {query}")
                print(f"生成的Cypher: {cypher}")
                
                # 执行查询
                results = await agent.query_by_text(query, generate_cypher=True)
                print(f"结果数量: {len(results)}")
                
                # 显示前3个结果
                for i, result in enumerate(results[:3]):
                    print(f"  - 结果 {i+1}: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
                print()
                
            except Exception as e:
                print(f"查询 '{query}' 失败: {e}\n")
        
        # 2. 预定义查询方法
        print("2. 预定义查询方法示例:")
        
        # 查找装配体的零件
        try:
            results = await agent.find_assembly_parts("Engine")
            print(f"查找包含'Engine'的装配体中的零件:")
            print(f"结果数量: {len(results)}")
            
            for i, result in enumerate(results[:3]):
                print(f"  - 零件 {i+1}: {result.get('part_name', 'Unknown')}")
                print(f"    ID: {result.get('part_id', 'N/A')}")
                print(f"    材料: {result.get('material', 'N/A')}")
                
        except Exception as e:
            print(f"查找装配体零件失败: {e}")
        
        # 查找具有特定特征的零件
        try:
            results = await agent.find_parts_with_features("hole")
            print(f"\n查找具有'hole'特征的零件:")
            print(f"结果数量: {len(results)}")
            
            for i, result in enumerate(results[:3]):
                print(f"  - 零件 {i+1}: {result.get('part_name', 'Unknown')}")
                print(f"    特征类型: {result.get('feature_type', 'N/A')}")
                print(f"    特征直径: {result.get('feature_diameter', 'N/A')}")
                
        except Exception as e:
            print(f"查找特征零件失败: {e}")
            
    except Exception as e:
        print(f"结构关系智能体演示失败: {e}")
    finally:
        await agent.disconnect()


async def demo_agent_manager():
    """演示智能体管理器的使用"""
    print("\n" + "="*50)
    print("演示智能体管理器")
    print("="*50)
    
    async with AgentManager() as manager:
        try:
            # 1. 文本搜索
            print("\n1. 统一接口文本搜索:")
            result = await manager.execute_text_search(
                "齿轮传动装置",
                top_k=3
            )
            print(f"查询状态: {result.status}")
            print(f"结果数量: {result.total_results}")
            
            for item in result.results:
                print(f"  - 排名 {item.rank}: {item.id}")
                print(f"    描述: {item.description[:80]}...")
                print(f"    相似度: {item.similarity_score:.3f}")
            
            # 2. 结构化查询
            print("\n2. 统一接口结构化查询:")
            result = await manager.execute_structured_query(
                "查找质量大于50克的铝制零件"
            )
            print(f"查询状态: {result.status}")
            print(f"结果数量: {result.total_results}")
            
            for item in result.results[:3]:
                print(f"  - 零件: {item.name}")
                print(f"    质量: {item.metadata.get('mass', 'N/A')}g")
                print(f"    材料: {item.metadata.get('material', 'N/A')}")
            
            # 3. 结构关系查询
            print("\n3. 统一接口结构关系查询:")
            result = await manager.execute_structural_query(
                "查找所有装配体的名称和ID"
            )
            print(f"查询状态: {result.status}")
            print(f"结果数量: {result.total_results}")
            
            for item in result.results[:3]:
                print(f"  - {item.name or item.id}")
                print(f"    类型: {item.search_type}")
            
            # 4. 多模态查询
            print("\n4. 多模态查询示例:")
            import numpy as np
            shape_vector = np.random.rand(768).tolist()
            
            result = await manager.execute_multimodal_query(
                text_query="轴承组件",
                shape_vector=shape_vector,
                material_filter="steel",
                mass_range=(10.0, 500.0),
                top_k=5
            )
            print(f"查询状态: {result.status}")
            print(f"执行的查询类型: {result.query_info.get('queries_executed', [])}")
            print(f"融合后结果数量: {result.total_results}")
            
            for item in result.results:
                print(f"  - 排名 {item.rank}: {item.name or item.id}")
                print(f"    搜索类型: {item.search_type}")
                print(f"    综合分数: {item.similarity_score:.3f}")
                
        except Exception as e:
            print(f"智能体管理器演示失败: {e}")


async def main():
    """主函数"""
    print("多智能体系统演示程序")
    print("本程序将演示三个专业检索智能体的功能:")
    print("1. 几何和语义查询智能体 (Milvus)")
    print("2. 结构化数据查询智能体 (PostgreSQL)")
    print("3. 结构关系智能体 (Neo4j)")
    print("4. 智能体管理器统一接口")
    
    try:
        # 演示各个智能体
        await demo_geometry_semantic_agent()
        await demo_structured_data_agent()
        await demo_structural_relationship_agent()
        await demo_agent_manager()
        
        print("\n" + "="*50)
        print("演示完成！")
        print("="*50)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"演示程序出错: {e}")
        print(f"演示程序出错: {e}")


if __name__ == "__main__":
    # 运行演示程序
    asyncio.run(main())
