from FlagEmbedding import BGEM3FlagModel

import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

model = BGEM3FlagModel('BAAI/bge-m3',  
                       use_fp16=True) # Setting use_fp16 to True speeds up computation with a slight performance degradation

sentences_1 = ['固定夹和夹体', '工艺采用冲压、弯曲']
description = """
这款固定夹用于将物体牢固地固定在特定位置，通常应用于机械设备、管道系统或实验室环境中。它整体呈L型结构，外形简洁稳固，能够提供可靠的支撑和约束。；组件：
；夹体：构成固定夹的主要框架，提供整体强度和支撑力。；固定板：位于夹体上方，用于与被固定物体接触并提供压紧力。；材料工艺：固定夹主要由钢材制成，具有较高的强度和
耐用性。可能采用冲压、弯曲等金属加工工艺成型，表面经过处理以提高防锈和耐磨性能。
"""
sentences_2 = [description, "样例文档"]

embeddings_1 = model.encode(sentences_1, 
                            batch_size=12, 
                            max_length=8192, # If you don't need such a long length, you can set a smaller value to speed up the encoding process.
                            )['dense_vecs']
embeddings_2 = model.encode(sentences_2)['dense_vecs']
similarity = embeddings_1 @ embeddings_2.T
print(similarity)
# [[0.6265, 0.3477], [0.3499, 0.678 ]]
