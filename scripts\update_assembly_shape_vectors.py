#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新装配体形状向量脚本

该脚本从CLIP特征文件加载装配体的形状向量，并将它们更新到Milvus数据库中。
它支持以下功能：
1. 从pickle文件加载CLIP特征
2. 将形状向量更新到Milvus数据库

使用方法:
    python scripts/update_assembly_shape_vectors.py --input dataset/clip_features.pkl --collection assembly_collection
"""

import os
import sys
import logging
import pickle
import argparse
import numpy as np
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_clip_features(input_path):
    """
    加载CLIP特征文件
    
    参数:
        input_path: CLIP特征文件路径
        
    返回:
        特征字典 {assembly_id: feature_vector}
    """
    logger.info(f"正在加载CLIP特征文件: {input_path}")
    
    try:
        with open(input_path, 'rb') as f:
            features = pickle.load(f)
        
        logger.info(f"成功加载特征文件，包含 {len(features)} 个装配体")
        return features
    except Exception as e:
        logger.error(f"加载特征文件失败: {e}")
        raise

def update_shape_vectors(
    clip_features, 
    collection_name="assembly_collection", 
    batch_size=100,
    device="cpu"
):
    """
    更新Milvus中的装配体形状向量
    
    参数:
        clip_features: CLIP特征字典 {assembly_id: feature_vector}
        collection_name: Milvus集合名称
        batch_size: 批处理大小，控制内存使用
        device: 使用的设备，'cpu'或'cuda'
        
    返回:
        更新的装配体数量
    """
    logger.info(f"开始更新形状向量")
    
    # 初始化Milvus管理器
    milvus_manager = MilvusManager(device=device)
    
    # 准备要更新的数据
    assembly_ids = []
    shape_vectors = []
    
    # 处理每个装配体的特征
    for assembly_id, feature_vector in tqdm(clip_features.items(), desc="处理特征"):
        try:
            # 归一化特征向量
            norm = np.linalg.norm(feature_vector)
            if norm > 0:
                feature_vector = feature_vector / norm
            
            # # 确保feature_vector是list类型
            # feature_vector = feature_vector.tolist()
                
            assembly_ids.append(assembly_id)
            shape_vectors.append(feature_vector)
            
            # 当积累了足够多的数据，执行批量更新
            if len(assembly_ids) >= batch_size:
                milvus_manager.update_shape_embeddings(
                    collection_name=collection_name,
                    assembly_ids=assembly_ids,
                    shape_embeddings=shape_vectors
                )
                # 清空缓存数据
                assembly_ids = []
                shape_vectors = []
                
        except Exception as e:
            logger.error(f"处理装配体 {assembly_id} 时出错: {e}")
    
    # 处理剩余的数据
    if assembly_ids:
        milvus_manager.update_shape_embeddings(
            collection_name=collection_name,
            assembly_ids=assembly_ids,
            shape_embeddings=shape_vectors
        )
    
    logger.info(f"形状向量更新完成，总共处理 {len(clip_features)} 个装配体")
    return len(clip_features)

def main():
    parser = argparse.ArgumentParser(description="更新Milvus装配体形状向量")
    parser.add_argument('--input', type=str, default='dataset/assembly_clip_features.pkl', help='CLIP特征文件路径')
    parser.add_argument('--collection', type=str, default='assembly_collection', help='Milvus集合名称')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    parser.add_argument('--device', type=str, choices=['cpu', 'cuda'], default='cpu', help='计算设备')
    
    args = parser.parse_args()
    
    try:
        # 加载CLIP特征
        clip_features = load_clip_features(args.input)
        
        # 更新形状向量
        update_shape_vectors(
            clip_features=clip_features,
            collection_name=args.collection,
            batch_size=args.batch_size,
            device=args.device
        )
        
        logger.info("形状向量更新成功")
        
    except Exception as e:
        logger.error(f"更新形状向量失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 