"""
结构化数据查询智能体 (Structured Data Query Agent)
与PostgreSQL关系型数据库交互，实现Text2SQL功能
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from pydantic import BaseModel
from src.agent.base_agent import BaseAgent
from src.agent.data_models import QueryResult, UnifiedStructuredDataTask, SearchResultItem
from src.models.LLM import MultiProviderLLM
from src.config import Config

logger = logging.getLogger(__name__)


class StructuredQueryResult(BaseModel):
    """结构化查询结果模型"""
    success: bool
    sql_query: Optional[str] = None
    results: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class StructuredDataAgent(BaseAgent):
    """
    结构化数据查询智能体
    
    该智能体专门处理基于物理属性、分类标签及其他元数据的精确查询任务。
    它将用户的自然语言查询转换为SQL语句，与PostgreSQL数据库交互。
    """
    
    def __init__(self, agent_name: str = "StructuredDataAgent"):
        super().__init__(agent_name)
        self.conn = None
        self.cursor = None
        self.llm = None
        self.schema_info = None
        self._connected = False
        
    async def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            # 建立数据库连接
            db_config = Config.POSTGRES_CONFIG
            self.conn = psycopg2.connect(**db_config)
            self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            
            # 初始化LLM
            self.llm = MultiProviderLLM()
            
            # 获取数据库模式信息
            await self._load_schema_info()
            
            logger.info(f"{self.agent_name} 成功连接到PostgreSQL数据库")
            
        except Exception as e:
            logger.error(f"{self.agent_name} 连接PostgreSQL失败: {e}")
            raise
    
    async def disconnect(self):
        """断开数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logger.info(f"{self.agent_name} 已断开PostgreSQL连接")
        except Exception as e:
            logger.error(f"断开连接时出错: {e}")
    
    async def _load_schema_info(self):
        """加载数据库模式信息"""
        try:
            # 获取表结构信息
            schema_query = """
            SELECT 
                table_name,
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_schema = 'cad_rag' OR table_name = 'cad_rag_parts'
            ORDER BY table_name, ordinal_position;
            """
            
            self.cursor.execute(schema_query)
            columns_info = self.cursor.fetchall()
            
            # 组织模式信息
            self.schema_info = {
                "tables": {},
                "description": "CAD零件数据库，包含装配体和零件的物理属性信息"
            }
            
            current_table = None
            for row in columns_info:
                table_name = row['table_name']
                if table_name not in self.schema_info["tables"]:
                    self.schema_info["tables"][table_name] = {
                        "columns": [],
                        "description": self._get_table_description(table_name)
                    }
                
                self.schema_info["tables"][table_name]["columns"].append({
                    "name": row['column_name'],
                    "type": row['data_type'],
                    "nullable": row['is_nullable'] == 'YES',
                    "default": row['column_default'],
                    "description": self._get_column_description(row['column_name'])
                })
            
            logger.info("数据库模式信息加载完成")
            
        except Exception as e:
            logger.error(f"加载数据库模式信息失败: {e}")
            # 使用默认模式信息
            self.schema_info = self._get_default_schema()
    
    def _get_table_description(self, table_name: str) -> str:
        """获取表的描述信息"""
        descriptions = {
            "parts": "CAD零件表，存储零件的几何属性、材料属性、制造特征等信息"
        }
        return descriptions.get(table_name, "未知表")
    
    def _get_column_description(self, column_name: str) -> str:
        """获取列的描述信息"""
        descriptions = {
            "uuid": "零件唯一标识符",
            "top_level_assembly_id": "顶层装配体ID",
            "name": "零件名称",
            "material": "材料类型",
            "description": "零件描述",
            "length": "长度（毫米）",
            "width": "宽度（毫米）",
            "height": "高度（毫米）",
            "area": "表面积（平方毫米）",
            "volume": "体积（立方毫米）",
            "density": "密度（克/立方厘米）",
            "mass": "质量（克）",
            "hole_count": "孔的数量",
            "hole_diameter_mean": "孔的平均直径（毫米）",
            "hole_diameter_std": "孔直径的标准差",
            "hole_depth_mean": "孔的平均深度（毫米）",
            "hole_depth_std": "孔深度的标准差"
        }
        return descriptions.get(column_name, "")
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认的数据库模式信息"""
        return {
            "tables": {
                "cad_rag.parts": {
                    "description": "CAD零件表，存储零件的几何属性、材料属性、制造特征等信息",
                    "columns": [
                        {"name": "uuid", "type": "text", "description": "零件唯一标识符"},
                        {"name": "top_level_assembly_id", "type": "text", "description": "顶层装配体ID"},
                        {"name": "name", "type": "text", "description": "零件名称"},
                        {"name": "material", "type": "text", "description": "材料类型"},
                        {"name": "description", "type": "text", "description": "零件描述"},
                        {"name": "length", "type": "float", "description": "长度（毫米）"},
                        {"name": "width", "type": "float", "description": "宽度（毫米）"},
                        {"name": "height", "type": "float", "description": "高度（毫米）"},
                        {"name": "area", "type": "float", "description": "表面积（平方毫米）"},
                        {"name": "volume", "type": "float", "description": "体积（立方毫米）"},
                        {"name": "density", "type": "float", "description": "密度（克/立方厘米）"},
                        {"name": "mass", "type": "float", "description": "质量（克）"},
                        {"name": "hole_count", "type": "integer", "description": "孔的数量"},
                        {"name": "hole_diameter_mean", "type": "float", "description": "孔的平均直径（毫米）"},
                        {"name": "hole_diameter_std", "type": "float", "description": "孔直径的标准差"},
                        {"name": "hole_depth_mean", "type": "float", "description": "孔的平均深度（毫米）"},
                        {"name": "hole_depth_std", "type": "float", "description": "孔深度的标准差"}
                    ]
                }
            },
            "description": "CAD零件数据库，包含装配体和零件的物理属性信息"
        }
    
    def _build_system_prompt(self) -> str:
        """构建Text2SQL的系统提示词"""
        schema_str = self._format_schema_for_prompt()
        
        return f"""你是一个专业的SQL查询生成器。你的任务是将用户的自然语言问题转换为准确的PostgreSQL SQL查询语句。

数据库模式信息:
{schema_str}

重要规则:
1. 只返回SQL查询语句，不要包含任何解释或前言
2. 查询语句必须符合PostgreSQL语法
3. 使用正确的表名和列名
4. 对于模糊查询，使用ILIKE或SIMILAR TO进行模糊匹配
5. 对于数值范围查询，使用BETWEEN或比较操作符
6. 限制返回结果数量，通常使用LIMIT 50
7. 优先使用表名 'cad_rag.parts'，如果不存在则使用 'parts'
8. 对于物理属性查询，注意单位换算和合理的数值范围
9. 使用适当的ORDER BY子句对结果进行排序

常见查询示例:
- 查找质量在某范围内的零件: WHERE mass BETWEEN 10 AND 100
- 查找特定材料的零件: WHERE material ILIKE '%steel%'
- 查找有孔的零件: WHERE hole_count > 0
- 查找大体积零件: WHERE volume > 1000"""
    
    def _format_schema_for_prompt(self) -> str:
        """格式化数据库模式信息用于提示词"""
        if not self.schema_info:
            return "数据库模式信息未加载"
        
        schema_parts = []
        for table_name, table_info in self.schema_info["tables"].items():
            schema_parts.append(f"\n表名: {table_name}")
            schema_parts.append(f"描述: {table_info['description']}")
            schema_parts.append("列信息:")
            
            for col in table_info["columns"]:
                col_desc = f"  - {col['name']} ({col['type']})"
                if col.get('description'):
                    col_desc += f": {col['description']}"
                schema_parts.append(col_desc)
        
        return "\n".join(schema_parts)
    
    async def execute_task(self, task: UnifiedStructuredDataTask) -> QueryResult:
        """
        执行结构化数据查询任务

        Args:
            task: UnifiedStructuredDataTask实例，包含自然语言查询文本和可选的ID列表

        Returns:
            QueryResult: 包含查询结果的查询结果对象
        """
        import time
        start_time = time.time()

        try:
            if self.conn is None:
                await self.connect()

            # 将自然语言转换为SQL查询
            sql_query = await self.text_to_sql(task.query_text)

            # 如果提供了ID列表，修改SQL查询以包含ID过滤
            if task.id_list:
                sql_query = self._add_id_filter_to_sql(sql_query, task.id_list)

            # 执行SQL查询
            raw_results = await self._execute_sql_query(sql_query)

            # 转换为标准化的SearchResultItem格式
            search_results = []
            for i, result in enumerate(raw_results[:task.top_k]):
                search_results.append(SearchResultItem(
                    rank=i + 1,
                    id=result.get('uuid', str(i)),
                    name=result.get('name', f'Part_{i}'),
                    description=result.get('description', ''),
                    search_type='structured',
                    metadata=result
                ))

            execution_time = time.time() - start_time

            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=search_results,
                execution_time=execution_time,
                total_results=len(search_results)
            )

        except Exception as e:
            logger.error(f"{self.agent_name} 执行任务失败: {e}")
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[],
                execution_time=execution_time,
                total_results=0
            )

    def _add_id_filter_to_sql(self, sql_query: str, id_list: List[str]) -> str:
        """
        向SQL查询添加ID过滤条件

        Args:
            sql_query: 原始SQL查询
            id_list: ID列表

        Returns:
            修改后的SQL查询
        """
        try:
            # 移除末尾的分号
            sql_query = sql_query.rstrip(';')

            # 构建ID过滤条件
            id_placeholders = ', '.join([f"'{id_val}'" for id_val in id_list])
            id_filter = f"uuid IN ({id_placeholders})"

            # 检查是否已有WHERE子句
            if 'WHERE' in sql_query.upper():
                # 添加AND条件
                sql_query += f" AND {id_filter}"
            else:
                # 添加WHERE子句
                sql_query += f" WHERE {id_filter}"

            # 重新添加分号
            sql_query += ';'

            logger.info(f"添加ID过滤条件: {id_filter}")
            return sql_query

        except Exception as e:
            logger.error(f"添加ID过滤条件失败: {e}")
            return sql_query

    async def _execute_sql_query(self, sql_query: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询
        
        Args:
            sql_query: SQL查询语句
            
        Returns:
            查询结果列表
        """
        try:
            self.cursor.execute(sql_query)
            rows = self.cursor.fetchall()
            
            # 转换为字典列表
            results = [dict(row) for row in rows]
            
            logger.info(f"SQL查询成功，返回{len(results)}条记录")
            return results
            
        except Exception as e:
            logger.error(f"SQL查询执行失败: {e}")
            raise
    
    async def text_to_sql(self, query_text: str) -> str:
        """
        将自然语言查询转换为SQL语句
        
        Args:
            query_text: 自然语言查询
            
        Returns:
            生成的SQL查询语句
        """
        try:
            if self.llm is None:
                await self.connect()
            
            system_prompt = self._build_system_prompt()
              # 构建完整的提示词
            full_prompt = f"{system_prompt}\n\n用户查询: {query_text}\n\nSQL查询语句:"

            # 调用LLM生成SQL - 修复：传入正确的消息格式
            response = self.llm.chat([{"role": "user", "content": full_prompt}])
            
            # 清理响应，提取SQL语句
            sql_query = self._clean_sql_response(response)
            
            logger.info(f"Text2SQL转换完成: {query_text} -> {sql_query}")
            return sql_query
            
        except Exception as e:
            logger.error(f"Text2SQL转换失败: {e}")
            raise
    
    def _clean_sql_response(self, response: str) -> str:
        """
        清理LLM响应，提取纯净的SQL语句
        
        Args:
            response: LLM的原始响应
            
        Returns:
            清理后的SQL语句
        """
        # 移除markdown代码块标记
        response = response.strip()
        if response.startswith("```sql"):
            response = response[6:]
        elif response.startswith("```"):
            response = response[3:]
        
        if response.endswith("```"):
            response = response[:-3]
        
        # 移除多余的空行和注释
        lines = response.split('\n')
        clean_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('--'):
                clean_lines.append(line)
        
        sql_query = ' '.join(clean_lines)
        
        # 确保以分号结尾
        if not sql_query.endswith(';'):
            sql_query += ';'
        
        return sql_query
    

