import json
import logging
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, Any
from src.knowledge_graph import CADKnowledgeGraph
from src.data_formats import CADModelData
from src.data_converters import get_converter
from src.fusion360_extractor import Fusion360Extractor
from src.models.clip import CLIPFeatureExtractor
from src.utils.file_utils import get_fusion360_test_paths

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def import_from_fusion360(file_path: str, kg: CADKnowledgeGraph, clip_extractor=None) -> str:
    """
    从Fusion360 JSON文件导入到知识图谱
    
    Args:
        file_path: Fusion360 JSON文件路径
        kg: 知识图谱实例
        clip_extractor: CLIP特征提取器实例，如不传入则创建新的
        
    Returns:
        str: 装配体ID
    """
    # 创建Fusion360提取器
    extractor = Fusion360Extractor(file_path, clip_extractor, extract_shape_embedding=False)
    
    # 从文件提取数据
    cad_data = extractor.convert()

    # TODO 打印cad_data的装配层次结构树
    # 调用CADModelData的方法打印装配层次结构树
    cad_data.print_assembly_tree()
    
    # 导入到知识图谱
    assembly_id = kg.import_from_unified_format(cad_data)
    logging.info(f"成功导入装配体，ID: {assembly_id}")
    return assembly_id

def batch_import_from_fusion360(root_dir: str, kg: CADKnowledgeGraph, clip_extractor=None) -> Dict[str, str]:
    """
    批量从指定根目录下的所有子文件夹中的assembly.json导入到知识图谱
    
    Args:
        root_dir: 根目录路径，每个子文件夹下应有assembly.json
        kg: 知识图谱实例
        clip_extractor: CLIP特征提取器实例，如不传入则创建新的
    
    Returns:
        Dict[str, str]: {子文件夹路径: 装配体ID}
    """
    imported = {}
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if 'assembly.json' in filenames:
            assembly_path = os.path.join(dirpath, 'assembly.json')
            try:
                assembly_id = import_from_fusion360(assembly_path, kg, clip_extractor)
                imported[dirpath] = assembly_id
            except Exception as e:
                logging.error(f"导入{assembly_path}失败: {e}")
    return imported

if __name__ == "__main__":
    # 创建CLIP特征提取器实例，只初始化一次
    # clip_extractor = CLIPFeatureExtractor()
    
    # 创建知识图谱实例
    kg = CADKnowledgeGraph.from_config()
    
    try:
        # 示例1：从Fusion360文件导入
        # assembly_id = import_from_fusion360(r"datasets\fusion360_assembly\40938_94635775\assembly.json", kg)
        
        # 示例2：从文件夹批量导入
        # result = batch_import_from_fusion360("test", kg)
        # print(result) 

        # 示例3：使用get_fusion360_test_paths获取测试集的所有文件夹，并批量导入到知识图谱
        test_paths = get_fusion360_test_paths()
        print(f"发现 {len(test_paths)} 个测试集文件夹")
        
        test_results = {}
        for test_path in test_paths:
            assembly_json = os.path.join(test_path, "assembly.json")
            if os.path.exists(assembly_json):
                print(f"正在导入: {assembly_json}")
                try:
                    assembly_id = import_from_fusion360(assembly_json, kg)
                    test_results[test_path] = assembly_id
                except Exception as e:
                    logging.error(f"导入{assembly_json}失败: {e}")
                    test_results[test_path] = f"导入失败: {str(e)}"
            else:
                print(f"警告: assembly.json不存在于路径: {test_path}")
        
        print("测试集导入结果:")
        print(json.dumps(test_results, indent=2, ensure_ascii=False))
    finally:
        # 关闭知识图谱连接
        kg.close() 