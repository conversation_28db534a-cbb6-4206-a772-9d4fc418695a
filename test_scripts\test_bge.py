from sentence_transformers import SentenceTransformer
queries = ['固定夹和夹体', '工艺采用冲压、弯曲']
description = """
这款固定夹用于将物体牢固地固定在特定位置，通常应用于机械设备、管道系统或实验室环境中。它整体呈L型结构，外形简洁稳固，能够提供可靠的支撑和约束。；组件：
；夹体：构成固定夹的主要框架，提供整体强度和支撑力。；固定板：位于夹体上方，用于与被固定物体接触并提供压紧力。；材料工艺：固定夹主要由钢材制成，具有较高的强度和
耐用性。可能采用冲压、弯曲等金属加工工艺成型，表面经过处理以提高防锈和耐磨性能。
"""
passages = [description, "样例文档"]
instruction = "为这个句子生成表示以用于检索相关文章："

model = SentenceTransformer('BAAI/bge-large-zh-v1.5')
q_embeddings = model.encode([instruction+q for q in queries], normalize_embeddings=True)
# q_embeddings = model.encode(queries, normalize_embeddings=True)
p_embeddings = model.encode(passages, normalize_embeddings=True)
scores = q_embeddings @ p_embeddings.T

print(scores)